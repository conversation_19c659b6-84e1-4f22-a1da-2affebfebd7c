<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'id_delegation' => $this->id_delegation,
            'id_governorate' => $this->id_governorate,
            'delegation' => new DelegationResource($this->whenLoaded('delegation')),
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            //'trips_as_start' => TripResource::collection($this->whenLoaded('tripsAsStart')),
            //'trips_as_end' => TripResource::collection($this->whenLoaded('tripsAsEnd')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
