<?php

namespace App\Repositories;

use App\Models\Transaction;
use Prettus\Repository\Eloquent\BaseRepository;

class TransactionRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'subscription_id' => '=',
        'client_id' => '=',
        'payment_mode' => '=', // 'en_ligne' (Online), 'guichet' (Guichet)
        'payment_method_id' => '=',
        'status' => '=', // 'completed', 'pending', 'failed', 'refunded'
        'transaction_reference' => 'like',
        'online_gateway' => 'like',
        'sale_point_id' => '=',
        'employee_id' => '=',
        'payment_date' => 'between',
        'amount' => 'between'
    ];

    public function model(): string
    {
        return Transaction::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
