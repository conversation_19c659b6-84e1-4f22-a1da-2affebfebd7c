<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriberStepOneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'lastname' => ['required', 'string', 'max:255'],
            'firstname' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'size:8', 'unique:subscribers,phone'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         'lastname.required'  => [
    //             'en' => 'The lastname field is required.',
    //             'fr' => 'Le champ nom est obligatoire.',
    //             'ar' => 'حقل اللقب مطلوب.',
    //         ],
    //         'lastname.max'       => [
    //             'en' => 'The lastname must not exceed 255 characters.',
    //             'fr' => 'Le nom ne doit pas dépasser 255 caractères.',
    //             'ar' => 'يجب ألا يتجاوز اللقب 255 حرفًا.',
    //         ],
    //         'firstname.required' => [
    //             'en' => 'The firstname field is required.',
    //             'fr' => 'Le champ prénom est obligatoire.',
    //             'ar' => 'حقل الاسم الشخصي مطلوب.',
    //         ],
    //         'firstname.max'      => [
    //             'en' => 'The firstname must not exceed 255 characters.',
    //             'fr' => 'Le prénom ne doit pas dépasser 255 caractères.',
    //             'ar' => 'يجب ألا يتجاوز الاسم الشخصي 255 حرفًا.',
    //         ],
    //         'phone.required'     => [
    //             'en' => 'The phone field is required.',
    //             'fr' => 'Le champ téléphone est obligatoire.',
    //             'ar' => 'حقل رقم الهاتف مطلوب.',
    //         ],
    //         'phone.size'         => [
    //             'en' => 'The size of phone must be 8.',
    //             'fr' => 'Le numéro de téléphone doit comporter 8 caractères.',
    //             'ar' => 'يجب أن يكون طول رقم الهاتف 8 أرقام.',
    //         ],
    //         'phone.unique'       => [
    //             'en' => 'The phone has already been taken.',
    //             'fr' => 'Ce numéro de téléphone est déjà utilisé.',
    //             'ar' => 'رقم الهاتف مستخدم بالفعل.',
    //         ],
    //     ];
    // }
}
