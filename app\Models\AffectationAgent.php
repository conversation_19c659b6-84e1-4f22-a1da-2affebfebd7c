<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AffectationAgent extends Model
{
    protected $fillable = [
        'id_agent',
        'id_sale_point',
        'id_sale_period'
    ];

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'id_agent');
    }

    public function salePoint(): BelongsTo
    {
        return $this->belongsTo(SalePoint::class, 'id_sale_point');
    }

    public function salePeriod(): BelongsTo
    {
        return $this->belongsTo(SalePeriod::class, 'id_sale_period');
    }

    public function affectationCardTypes(): HasMany
    {
        return $this->hasMany(AffectationCardType::class, 'id_affectation_agent');
    }
}
