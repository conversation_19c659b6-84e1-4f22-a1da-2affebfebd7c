<?php

use App\Http\Controllers\ConfigController;
use App\Http\Controllers\SocialAffairController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CaptchaController;
use App\Http\Controllers\CardFeeController;
use App\Http\Controllers\CardTypeController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\DegreeController;
use App\Http\Controllers\DelegationController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\EstablishmentController;
use App\Http\Controllers\GovernorateController;
use App\Http\Controllers\GovernoratePurchaseOrderController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\MotifDuplicateController;
use App\Http\Controllers\PeriodicityController;
use App\Http\Controllers\SalePeriodController;
use App\Http\Controllers\SeasonController;
use App\Http\Controllers\StationController;
use App\Http\Controllers\SubscriberController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SubsTypeController;
use App\Http\Controllers\TariffBaseController;
use App\Http\Controllers\TariffOptionController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\TypeClientController;
use App\Http\Controllers\TypeEstablishmentController;
use App\Http\Controllers\VerifApiController;
use App\Models\Subscriber;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('/captcha',  [CaptchaController::class, 'generateCaptcha']);
Route::post('/login', [AuthController::class, 'login'])->middleware('throttle:5,1');

// Register Routes
Route::post('/store-subscriber', [SubscriberController::class, 'store']);
Route::post('/store-subscriber-step-one', [SubscriberController::class, 'storeStepOne']);
Route::post('/store-subscriber-step-two', [SubscriberController::class, 'storeStepTwo']);
Route::post('/store-subscriber-step-three', [SubscriberController::class, 'storeStepThree']);
Route::post('/store-subscriber-step-four', [SubscriberController::class, 'storeStepFour']);

// Reset Password Routes
Route::post('/reset-password-step-one', [SubscriberController::class, 'resetPasswordStepOne']);
Route::post('/reset-password-step-two', [SubscriberController::class, 'resetPasswordStepTwo']);
Route::post('/reset-password', [SubscriberController::class, 'resetPassword']);

Route::group(
    [
        'where' => ['id' => '[0-9]+'],
        'middleware' => ['jwt.auth'],
    ],
    function () {
        // Auth Routes
        Route::get('/verify-token', [AuthController::class, 'me']);
        Route::post('/refresh-token', [AuthController::class, 'refresh']);
        Route::get('/logout', [AuthController::class, 'logout']);

        // Update Profile Routes
        Route::post('/update-profile', [SubscriberController::class, 'updateProfile']);
        Route::post('/update-password', [SubscriberController::class, 'UpdatePassword']);
        Route::post('/update-email-step-one', [SubscriberController::class, 'updateEmailStepOne']);
        Route::post('/update-email-step-two', [SubscriberController::class, 'updateEmailStepTwo']);
        Route::post('/update-email-step-three', [SubscriberController::class, 'updateEmailStepThree']);
        Route::post('/update-email', [SubscriberController::class, 'updateEmail']);

        // Manage Subscriptions routes
        Route::get('/governorates-all', [GovernorateController::class, 'all']);
        Route::apiResource('governorates', GovernorateController::class);

        Route::get('/delegations-all', [DelegationController::class, 'all']);
        Route::get('/delegations/governorate/{governorate}', [DelegationController::class, 'getByGovernorate']);
        Route::apiResource('delegations', DelegationController::class);

        Route::get('/clients/delegation/{delegation}', [ClientController::class, 'getByDelegation']);
        Route::get('/clients/governorate/{governorate}', [ClientController::class, 'getByGovernorate']);
        Route::get('/clients/type/{type}', [ClientController::class, 'getByClientType']);
        Route::post('/clients/search-student', [ClientController::class, 'searchStudent']);
        Route::get('/clients-has-cin', [ClientController::class, 'getClientsHasCIN']);
        Route::get('/clients-impersonal', [ClientController::class, 'getImpersonalClients']);
        Route::get('/clients-conventional', [ClientController::class, 'getConventionalClients']);
        Route::apiResource('clients', ClientController::class);


        Route::get('/governorate-purchase-orders/{governorate}', [GovernoratePurchaseOrderController::class, 'getByGovernorate']);
        Route::apiResource('governorate-purchase-orders', GovernoratePurchaseOrderController::class);

        Route::get('/motif-duplicates-all', [MotifDuplicateController::class, 'all']);
        Route::apiResource('motif-duplicates', MotifDuplicateController::class);

        Route::get('/establishments-all', [EstablishmentController::class, 'all']);
        Route::get('/establishments/delegation/{delegation}', [EstablishmentController::class, 'getByDelegation']);
        Route::get('/establishments/type/{type}', [EstablishmentController::class, 'getByType']);
        Route::apiResource('establishments', EstablishmentController::class);

        Route::get('/type-establishments-all', [TypeEstablishmentController::class, 'all']);
        Route::apiResource('type-establishments', TypeEstablishmentController::class);

        Route::get('/degrees-all', [DegreeController::class, 'all']);
        Route::get('/degrees/type/{type}', [DegreeController::class, 'getByType']);
        Route::apiResource('degrees', DegreeController::class);

        Route::get('/card-types-all', [CardTypeController::class, 'all']);
        Route::apiResource('card-types', CardTypeController::class);

        Route::get('/subs-types-all', [SubsTypeController::class, 'all']);
        Route::apiResource('subs-types', SubsTypeController::class);

        Route::get('/tariff-bases-all', [TariffBaseController::class, 'all']);
        Route::apiResource('tariff-bases', TariffBaseController::class);

        Route::get('/lines-all', [LineController::class, 'all']);
        Route::post('/lines/assign-stations', [LineController::class, 'assignStations']);
        Route::get('/lines/by-trip/{tripId}', [LineController::class, 'getLinesByTrip']);
        Route::get('/lines/by-stations/{departureStationId}/{arrivalStationId}', [LineController::class, 'getLinesByStations']);
        Route::get('/lines/{line}/stations-routes', [LineController::class, 'getStationsAndRoutes']);
        Route::put('lines/{line}/stations-routes', [LineController::class, 'updateStationsAndRoutes'])
            ->name('lines.update-stations-routes');
        Route::apiResource('lines', LineController::class);

        Route::get('/stations-all', [StationController::class, 'all']);
        Route::get('/stations/subscription-type/{subsTypeId}', [StationController::class, 'getStationsBySubscriptionType']);
        Route::get('/stations/{stationId}/connected-stations/{subsTypeId}', [StationController::class, 'getConnectedStationsForTrip']);
        Route::apiResource('stations', StationController::class);

        Route::get('/seasons-all', [SeasonController::class, 'all']);
        Route::apiResource('seasons', SeasonController::class);

        Route::get('/trips-all', [TripController::class, 'all']);
        Route::apiResource('trips', TripController::class);

        Route::apiResource('tariff-options', TariffOptionController::class);

        Route::get('/campaigns/{campaign}/sales-periods', [SalePeriodController::class, 'getByCampaign']);
        Route::get('/campaigns-all', [CampaignController::class, 'all']);
        Route::apiResource('campaigns', CampaignController::class);

        Route::get('/sales-periods-all', [SalePeriodController::class, 'all']);
        Route::apiResource('sales-periods', SalePeriodController::class);

        Route::get('/type-clients-all', [TypeClientController::class, 'all']);
        Route::apiResource('type-clients', TypeClientController::class);

        Route::get('/periodicities-all', [PeriodicityController::class, 'all']);
        Route::apiResource('periodicities', PeriodicityController::class);

        Route::get('/discounts-all', [DiscountController::class, 'all']);
        Route::apiResource('discounts', DiscountController::class);

        Route::get('/card-fees-all', [CardFeeController::class, 'all']);
        Route::apiResource('card-fees', CardFeeController::class);

        // Config Routes
        Route::get('/configs-all', [ConfigController::class, 'all']);

        // Subscription Routes
        Route::get('/subscriptions-all', [SubscriptionController::class, 'all']);
        Route::get('/subscriptions/client/{clientId}', [SubscriptionController::class, 'getByClient']);
        Route::get('/subscriptions/last', [SubscriptionController::class, 'getLastSubscriptionByClient']);
        Route::get('/subscriptions/subs-type/{subsTypeId}', [SubscriptionController::class, 'getBySubsType']);
        Route::get('/subscriptions/trip/{tripId}', [SubscriptionController::class, 'getByTrip']);
        Route::get('/subscriptions/periodicity/{periodicityId}', [SubscriptionController::class, 'getByPeriodicity']);
        Route::get('/subscriptions/parent/{parentId}', [SubscriptionController::class, 'getByParent']);
        Route::apiResource('subscriptions', SubscriptionController::class);

        Route::post('/social-affairs/verify', [SocialAffairController::class, 'verifyCinParent']);
        Route::post('/transactions/process-payment', [TransactionController::class, 'processPayment']);

        // Verif subscriber
        Route::post('/subscriptions/verif/eleve', [VerifApiController::class, 'verifEleve']);
        Route::post('/subscriptions/verif/etudiant', [VerifApiController::class, 'verifEtudiant']);
        Route::post('/subscriptions/verif/civile', [VerifApiController::class, 'verifCitoyen']);
        Route::post('/subscriptions/verif/social', [VerifApiController::class, 'verifCitoyenFamille']);
    }
);


