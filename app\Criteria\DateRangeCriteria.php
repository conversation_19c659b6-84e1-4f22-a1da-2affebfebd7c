<?php

namespace App\Criteria;

use Prettus\Repository\Contracts\CriteriaInterface;
use Prettus\Repository\Contracts\RepositoryInterface;

/**
 * Class DateRangeCriteria
 * 
 * This criteria handles date range filtering for the created_at field
 * using created_at_start and created_at_end parameters from the request
 */
class DateRangeCriteria implements CriteriaInterface
{
    /**
     * Apply criteria in current Query
     *
     * @param $model
     * @param RepositoryInterface $repository
     * @return mixed
     */
    public function apply($model, RepositoryInterface $repository)
    {
        // Handle date range filtering from request parameters
        if (request()->has('search')) {
            $searchParams = request()->get('search');
            
            // Parse search parameters
            if (is_string($searchParams)) {
                $searchPairs = explode(';', $searchParams);
                
                foreach ($searchPairs as $pair) {
                    if (strpos($pair, ':') !== false) {
                        [$field, $value] = explode(':', $pair, 2);
                        
                        // Handle created_at_start
                        if ($field === 'created_at_start' && !empty($value)) {
                            $model = $model->where('created_at', '>=', $value . ' 00:00:00');
                        }
                        
                        // Handle created_at_end
                        if ($field === 'created_at_end' && !empty($value)) {
                            $model = $model->where('created_at', '<=', $value . ' 23:59:59');
                        }
                    }
                }
            }
        }
        
        return $model;
    }
}
