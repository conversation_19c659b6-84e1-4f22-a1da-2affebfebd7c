<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\TripResource;
use App\Http\Resources\PeriodicityResource;
use App\Http\Resources\SubsTypeResource;
use App\Http\Resources\ClientResource;
use App\Http\Resources\PaymentMethodResource;
use App\Http\Resources\SubsCardResource;
use App\Http\Resources\TransactionResource;

class SubsDuplicationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'motif_duplicate_id' => $this->motif_duplicate_id,
            'admin_id' => $this->admin_id,
            'subscription_id' => $this->subscription_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relations
            // 'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            // 'client' => new ClientResource($this->whenLoaded('client')),
            // 'payment_method' => new PaymentMethodResource($this->whenLoaded('paymentMethod')),
            // 'trip' => new TripResource($this->whenLoaded('trip')),
            // 'periodicity' => new PeriodicityResource($this->whenLoaded('periodicity')),
            // 'parent_subscription' => new SubscriptionResource($this->whenLoaded('parentSubscription')),
            // 'child_subscriptions' => SubscriptionResource::collection($this->whenLoaded('childSubscriptions')),
            // 'subs_cards' => SubsCardResource::collection($this->whenLoaded('subsCards')),
            // 'motif_duplicate' => TransactionResource::collection($this->whenLoaded('transactions')),
        ];
    }
}

