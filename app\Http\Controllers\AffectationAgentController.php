<?php

namespace App\Http\Controllers;

use App\Models\AffectationAgent;
use App\Models\AffectationCardType;
use App\Models\CardType;
use App\Models\StockCard;
use App\Repositories\AffectationAgentRepository;
use App\Http\Requests\StoreAffectationAgentRequest;
use App\Http\Requests\UpdateAffectationAgentRequest;
use App\Http\Resources\AffectationAgentResource;
use App\Services\CardSequenceTracker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class AffectationAgentController extends Controller
{
    private AffectationAgentRepository $repository;
    protected $cardSequenceTracker;

    public function __construct(AffectationAgentRepository $repository, CardSequenceTracker $cardSequenceTracker)
    {
        $this->repository = $repository;
        $this->cardSequenceTracker = $cardSequenceTracker;
        $this->authorizeResource(AffectationAgent::class, 'affectation_agent');
    }


    public function all(): AnonymousResourceCollection
    {
        return AffectationAgentResource::collection(
            $this->repository->with(['agent', 'salePoint', 'salePeriod', 'affectationCardTypes.cardType'])->all()
        );
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $affectations = $this->repository->with(['agent', 'salePoint', 'salePeriod', 'affectationCardTypes.cardType'])
            ->latest()
            ->paginate($request->input('perPage'));

        // Get all card types
        $cardTypes = CardType::all();


        // Calculate max serial number for each card type
        $maxSerialNumbers = [];
        foreach ($cardTypes as $cardType) {
            $maxSerialNumber = AffectationCardType::where('id_card_type', $cardType->id)
                ->max('end_serial_number') ?? 0;

            $maxSerialNumbers[$cardType->id] = [
                'id' => $cardType->id,
                'max_serial_number' => $maxSerialNumber
            ];
        }

        return AffectationAgentResource::collection($affectations)
            ->additional([
                'meta' => [
                    'card_type_max_serials' => $maxSerialNumbers,

                ]
            ]);
    }

    public function store(StoreAffectationAgentRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Vérifier si l'affectation existe déjà
            $existingAffectation = $this->repository->findWhere([
                'id_agent' => $request->id_agent,
                'id_sale_point' => $request->id_sale_point,
                'id_sale_period' => $request->id_sale_period,
            ])->first();
            
            if ($existingAffectation) {
                $affectationAgent = $existingAffectation;
            } else {
                $data = [
                    'id_agent' => $request->id_agent,
                    'id_sale_point' => $request->id_sale_point,
                    'id_sale_period' => $request->id_sale_period,
                ];
                
                $affectationAgent = $this->repository->create($data);
            }


            foreach ($request->ranges as $range) {
                $affectationCardType = $affectationAgent->affectationCardTypes()->create([
                    'id_card_type' => $range['cardType'],
                    'start_serial_number' => $range['start_serial_number'],
                    'end_serial_number' => $range['end_serial_number'],
                    'current_serial_number' => $range['start_serial_number'],
                ]);

                // Update card sequences
                $this->cardSequenceTracker->assignToAgent(
                    $range['cardType'],
                    $range['start_serial_number'],
                    $range['end_serial_number'],
                    $request->id_agent
                );
            }

            DB::commit();

            return response()->json([
                'message' => 'Affectation created successfully',
                'data' => new AffectationAgentResource(
                    $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'affectationCardTypes.cardType'])
                )
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(AffectationAgent $affectationAgent): AffectationAgentResource
    {
        return new AffectationAgentResource(
            $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'affectationCardTypes.cardType'])
        );
    }

    public function update(UpdateAffectationAgentRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationAgent = $this->repository->find($id);

            if (!$affectationAgent) {
                return response()->json([
                    'message' => 'Affectation not found',
                    'error' => 'Resource not found'
                ], 404);
            }

            $data = [
                'id_agent' => $request->id_agent,
                'id_sale_point' => $request->id_sale_point,
                'id_sale_period' => $request->id_sale_period,
            ];

            $affectationAgent = $this->repository->update($data, $id);

            $affectationAgent->affectationCardTypes()->delete();


            foreach ($request->ranges as $range) {
                $affectationCardType = $affectationAgent->affectationCardTypes()->create([
                    'id_card_type' => $range['cardType'],
                    'start_serial_number' => $range['start_serial_number'],
                    'end_serial_number' => $range['end_serial_number'],
                    'current_serial_number' => $range['start_serial_number'],
                ]);

                // Update card sequences
                $this->cardSequenceTracker->assignToAgent(
                    $range['cardType'],
                    $range['start_serial_number'],
                    $range['end_serial_number'],
                    $request->id_agent
                );
            }
            DB::commit();

            return response()->json([
                'message' => 'Affectation updated successfully',
                'data' => new AffectationAgentResource(
                    $affectationAgent->load(['agent', 'salePoint', 'salePeriod', 'affectationCardTypes.cardType'])
                )
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $affectationAgent = $this->repository->find($id);

            if (!$affectationAgent) {
                return response()->json([
                    'message' => 'Affectation not found',
                    'error' => 'Resource not found'
                ], 404);
            }

            // Get all card types and sequences before deleting
            $cardTypes = $affectationAgent->affectationCardTypes;

            // Delete affectation card types
            $affectationAgent->affectationCardTypes()->delete();
            $this->repository->delete($id);

            // Return all sequences to stock
            foreach ($cardTypes as $cardType) {
                $this->cardSequenceTracker->returnToStock(
                    $cardType->id_card_type,
                    $cardType->start_serial_number,
                    $cardType->end_serial_number
                );
            }

            DB::commit();

            return response()->json([
                'message' => 'Affectation deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error deleting affectation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getAgentsBySalePoint($salePointId, $salePeriodId): JsonResponse
    {
        try {
            $salePoint = \App\Models\SalePoint::findOrFail($salePointId);
            $salePeriod = \App\Models\SalePeriod::findOrFail($salePeriodId);

            $affectations = AffectationAgent::query()
                ->where([
                    'id_sale_point' => $salePointId,
                    'id_sale_period' => $salePeriodId
                ])
                ->with([
                    'agent',
                    'salePoint',
                    'salePeriod',
                    'affectationCardTypes.cardType'
                ])
                ->get();

            return response()->json([
                'success' => true,
                'message' => $affectations->isEmpty()
                    ? 'No agents found for this sale point and period'
                    : 'Agents retrieved successfully',
                'data' => AffectationAgentResource::collection($affectations)
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sale point or sale period not found',
                'error' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving agents',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}



