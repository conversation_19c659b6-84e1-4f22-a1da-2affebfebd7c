<?php

namespace App\Repositories;

use App\Models\TypeClient;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TypeClientRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'color' => '=',
        'is_student' => '=',
        'hasCIN' => '=',
        'is_impersonal' => '=',
        'is_conventional' => '='
    ];

    public function model(): string
    {
        return TypeClient::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}

