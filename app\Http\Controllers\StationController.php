<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStationRequest;
use App\Http\Requests\UpdateStationRequest;
use App\Http\Resources\StationResource;
use App\Models\Station;
use App\Models\SubsType;
use App\Models\TariffOption;
use App\Models\Trip;
use App\Repositories\StationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;

class StationController extends Controller
{
    private StationRepository $repository;

    public function __construct(StationRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Station::class, 'station');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return StationResource::collection(
            $this->repository->with(['delegation', 'governorate'])->latest()->paginate($request->input('perPage', config('app.pagination', 10)))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return StationResource::collection(
            $this->repository->with(['delegation', 'governorate'])->all()
        );
    }

    public function store(StoreStationRequest $request): JsonResponse
    {
        $station = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Station created successfully',
            'data' => new StationResource($station->load(['delegation', 'governorate']))
        ], 201);
    }

    public function show(Station $station): StationResource
    {
        return new StationResource($station->load(['delegation', 'governorate']));
    }

    public function update(UpdateStationRequest $request, Station $station): JsonResponse
    {
        $station = $this->repository->update($request->validated(), $station->id);

        return response()->json([
            'message' => 'Station updated successfully',
            'data' => new StationResource($station->load(['delegation', 'governorate']))
        ]);
    }

    public function destroy(Station $station): JsonResponse
    {
        $this->repository->delete($station->id);

        return response()->json([
            'message' => 'Station deleted successfully'
        ]);
    }

    public function getStationsBySubscriptionType(int $subsTypeId, Request $request): AnonymousResourceCollection
    {
        SubsType::findOrFail($subsTypeId);

        $tripIds = TariffOption::where('id_subs_type', $subsTypeId)
            ->pluck('id_trip');

        $query = Station::query();

        if ($request->has('delegationId')) {
            $query->where('id_delegation', $request->input('delegationId'));
        }

        $query->where(function($q) use ($tripIds) {
            $q->whereHas('tripsAsStart', function($query) use ($tripIds) {
                    $query->whereIn('id', $tripIds);
                })
                ->orWhereHas('tripsAsEnd', function($query) use ($tripIds) {
                    $query->whereIn('id', $tripIds);
                });
        })
        ->with(['delegation', 'governorate']);

        $stations = $query->get();

        return StationResource::collection($stations);
    }

    public function getConnectedStationsForTrip(int $stationId, int $subsTypeId, Request $request): JsonResponse
    {
        Station::findOrFail($stationId);
        SubsType::findOrFail($subsTypeId);

        // Get all trip IDs that are allowed for this subscription type
        $tripIds = TariffOption::where('id_subs_type', $subsTypeId)
            ->pluck('id_trip');

        // Base query builder with common conditions
        $baseQuery = Station::query()
            ->with(['delegation', 'governorate']);

        // Add delegation filter if provided
        if ($request->has('delegationId')) {
            $baseQuery->where('id_delegation', $request->input('delegationId'));
        }

        // Get all end stations for trips that start with the selected station
        $endStations = (clone $baseQuery)
            ->whereHas('tripsAsEnd', function($query) use ($stationId, $tripIds) {
                $query->where('id_station_start', $stationId)
                      ->whereIn('id', $tripIds);
            })
            ->get();

        // Get all start stations for trips that end with the selected station
        $startStations = $baseQuery
            ->whereHas('tripsAsStart', function($query) use ($stationId, $tripIds) {
                $query->where('id_station_end', $stationId)
                      ->whereIn('id', $tripIds);
            })
            ->get();

        // Merge the two collections and remove duplicates
        $connectedStations = $endStations->merge($startStations)->unique('id');

        return response()->json([
            'data' => StationResource::collection($connectedStations)
        ]);
    }
}




