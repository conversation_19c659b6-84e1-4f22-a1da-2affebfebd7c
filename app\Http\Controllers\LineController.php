<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLineRequest;
use App\Http\Requests\UpdateLineRequest;
use App\Http\Requests\StoreLineStationAssignmentRequest;
use App\Http\Requests\UpdateLineStationAssignmentRequest;
use App\Http\Resources\LineResource;
use App\Models\Line;
use App\Models\Season;
use App\Models\Station;
use App\Models\Trip;
use App\Repositories\LineRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LineController extends Controller
{
    private LineRepository $repository;

    public function __construct(LineRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Line::class, 'line');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return LineResource::collection(
            $this->repository
                        ->latest()
                        ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return LineResource::collection($this->repository->where('status', 1)->get());
    }

    public function store(StoreLineRequest $request): JsonResponse
    {
        $line = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Line created successfully',
            'data' => new LineResource($line)
        ], 201);
    }

    public function show(Line $line): LineResource
    {
        return new LineResource($line);
    }

    public function update(UpdateLineRequest $request, Line $line): JsonResponse
    {
        $line = $this->repository->update($request->validated(), $line->id);

        return response()->json([
            'message' => 'Line updated successfully',
            'data' => new LineResource($line)
        ]);
    }

    public function destroy(Line $line): JsonResponse
    {
        try {
            DB::beginTransaction();

            foreach ($line->trips as $trip) {
                $trip->tariffOptions()->delete();
            }

            // Supprimer les trips
            $line->trips()->delete();

            // Supprimer les website_trips
            $line->websiteTrips()->delete();

            // Supprimer les line_stations
            $line->lineStations()->delete();

            // Supprimer la ligne
            $this->repository->delete($line->id);

            DB::commit();

            return response()->json([
                'message' => 'Line deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to delete line',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getStationsAndRoutes(Line $line): JsonResponse
    {
        try {
            $seasons = Season::all()->keyBy('id');

            $line->load([
                'lineStations' => function ($query) {
                    $query->orderBy('position');
                },
                'lineStations.station',
                'trips' => function ($query) {
                    $query->where('inter_station', true)
                        ->with(['startStation', 'endStation']);
                }
            ]);

            $formattedLine = [
                'id' => $line->id,
                'code_line' => $line->CODE_LINE,
                'nom_fr' => $line->nom_fr,
                'nom_ar' => $line->nom_ar,
                'nom_en' => $line->nom_en,
                'service_type' => strtoupper($line->type_service),
                'status' => $line->status ? 'ACTIF' : 'INACTIF',
                'created_at' => $line->created_at,
                'commercial_speed' => $line->commercial_speed,
                'stations' => $line->lineStations->map(function ($lineStation) use ($seasons) {
                    return [
                        'id' => $lineStation->station->id,
                        'nom_fr' => $lineStation->station->nom_fr,
                        'nom_ar' => $lineStation->station->nom_ar,
                        'nom_en' => $lineStation->station->nom_en,
                        'position' => $lineStation->position,
                        'type' => $lineStation->type,
                        'departure_times' => collect($lineStation->start_time ?: [])->map(function ($times, $seasonId) use ($seasons) {
                            $season = $seasons->get((int) $seasonId);
                            return [
                                'id_season' => (int) $seasonId,
                                'season_name' => $season ? [
                                    'nom_fr' => $season->nom_fr,
                                    'nom_en' => $season->nom_en,
                                    'nom_ar' => $season->nom_ar
                                ] : null,
                                'times' => $times
                            ];
                        })->values()->all()
                    ];
                })->values()->all(),
                'routes' => $line->trips->map(function ($trip) {
                    return [
                        'id' => $trip->id,
                        'number_of_km' => $trip->number_of_km,
                        'station_depart' => [
                            'id' => $trip->startStation->id,
                            'nom_fr' => $trip->startStation->nom_fr,
                            'nom_ar' => $trip->startStation->nom_ar,
                            'nom_er' => $trip->startStation->nom_er,
                        ],
                        'station_arrival' => [
                            'id' => $trip->endStation->id,
                            'nom_fr' => $trip->endStation->nom_fr,
                            'nom_ar' => $trip->endStation->nom_ar,
                            'nom_en' => $trip->endStation->nom_en,
                        ]
                    ];
                })->values()->all()
            ];

            return response()->json($formattedLine);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function assignStations(StoreLineStationAssignmentRequest $request): JsonResponse
    {
        try {
            \DB::beginTransaction();

            $line = Line::findOrFail($request->id_line);

            $line->lineStations()->delete();
            $line->trips()->delete();

            foreach ($request->validated()['stations'] as $stationData) {
                $lineStation = $line->lineStations()->create([
                    'id_station' => $stationData['id_station'],
                    'position' => $stationData['position'],
                    'type' => $stationData['type']
                ]);

                if (!empty($stationData['departure_times'])) {
                    $departureTimesJson = [];
                    foreach ($stationData['departure_times'] as $timeData) {
                        $departureTimesJson[$timeData['id_season']] = $timeData['times'];
                    }
                    $lineStation->update([
                        'start_time' => $departureTimesJson
                    ]);
                }
            }

            foreach ($request->validated()['routes'] as $routeData) {
                $line->trips()->create([
                    'nom_fr' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_en' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_ar' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'id_station_start' => $routeData['id_station_start'],
                    'id_station_end' => $routeData['id_station_end'],
                    'status' => $routeData['status'],
                    'inter_station' => $routeData['inter_station'],
                    'number_of_km' => $routeData['number_of_km']
                ]);
            }

            \DB::commit();
            return response()->json([
                'message' => 'Stations and routes assigned successfully',
                'data' => new LineResource($line->load(['lineStations.station', 'trips']))
            ]);
        }
        catch (\Exception $e) {
            \DB::rollBack();
            return response()->json([
                'message' => 'Failed to assign stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateStationsAndRoutes(UpdateLineStationAssignmentRequest $request): JsonResponse
    {
        try {
            \DB::beginTransaction();

            $line = Line::findOrFail($request->id_line);

            $line->lineStations()->delete();
            $line->trips()->delete();

            foreach ($request->validated()['stations'] as $stationData) {
                $lineStation = $line->lineStations()->create([
                    'id_station' => $stationData['id_station'],
                    'position' => $stationData['position'],
                    'type' => $stationData['type']
                ]);

                if (!empty($stationData['departure_times'])) {
                    $departureTimesJson = [];
                    foreach ($stationData['departure_times'] as $timeData) {
                        $departureTimesJson[$timeData['id_season']] = $timeData['times'];
                    }
                    $lineStation->update([
                        'start_time' => $departureTimesJson
                    ]);
                }
            }

            foreach ($request->validated()['routes'] as $routeData) {
                $line->trips()->create([
                    'nom_fr' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_en' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'nom_ar' => 'Route ' . $routeData['id_station_start'] . ' - ' . $routeData['id_station_end'],
                    'id_station_start' => $routeData['id_station_start'],
                    'id_station_end' => $routeData['id_station_end'],
                    'status' => $routeData['status'],
                    'inter_station' => $routeData['inter_station'],
                    'number_of_km' => $routeData['number_of_km']
                ]);
            }

            \DB::commit();
            return response()->json([
                'message' => 'Stations and routes updated successfully',
                'data' => new LineResource($line->load(['lineStations.station', 'trips']))
            ]);
        }
        catch (\Exception $e) {
            \DB::rollBack();
            return response()->json([
                'message' => 'Failed to update stations and routes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getLinesByTrip(int $tripId): AnonymousResourceCollection
    {
        Trip::findOrFail($tripId);

        $lines = Line::whereHas('trips', function($query) use ($tripId) {
                $query->where('id', $tripId);
            })
            ->get();

        return LineResource::collection($lines);
    }

    public function getLinesByStations(int $departureStationId, int $arrivalStationId): JsonResponse
    {
        // Verify that both stations exist
        Station::findOrFail($departureStationId);
        Station::findOrFail($arrivalStationId);

        // Get all lines that have trips between these two stations
        // This considers both directions
        $lines = Line::whereHas('trips', function($query) use ($departureStationId, $arrivalStationId) {
                $query->where(function($q) use ($departureStationId, $arrivalStationId) {
                    $q->where('id_station_start', $departureStationId)
                      ->where('id_station_end', $arrivalStationId)
                      ->where('inter_station', false);
                })->orWhere(function($q) use ($departureStationId, $arrivalStationId) {
                    $q->where('id_station_start', $arrivalStationId)
                      ->where('id_station_end', $departureStationId)
                      ->where('inter_station', false);
                });
            })
            ->with(['trips' => function($query) use ($departureStationId, $arrivalStationId) {
                $query->where(function($q) use ($departureStationId, $arrivalStationId) {
                    $q->where('id_station_start', $departureStationId)
                      ->where('id_station_end', $arrivalStationId)
                      ->where('inter_station', false);
                })->orWhere(function($q) use ($departureStationId, $arrivalStationId) {
                    $q->where('id_station_start', $arrivalStationId)
                      ->where('id_station_end', $departureStationId)
                      ->where('inter_station', false);
                });
            }])
            ->get();

        return response()->json([
            'data' => LineResource::collection($lines)
        ]);
    }
}

