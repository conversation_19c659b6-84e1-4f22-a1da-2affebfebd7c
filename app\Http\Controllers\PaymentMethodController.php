<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Http\Resources\PaymentMethodResource;
use App\Http\Requests\StorePaymentMethodRequest;
use App\Http\Requests\UpdatePaymentMethodRequest;
use App\Repositories\PaymentMethodRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class PaymentMethodController extends Controller
{
    private PaymentMethodRepository $repository;

    public function __construct(PaymentMethodRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(PaymentMethod::class, 'payment_method');
    }

    public function all(): AnonymousResourceCollection
    {
        return PaymentMethodResource::collection(
            $this->repository->all()
        );
    }

    public function index(): AnonymousResourceCollection
    {
        return PaymentMethodResource::collection($this->repository->latest()->paginate());
    }

    public function store(StorePaymentMethodRequest $request): PaymentMethodResource
    {
        return new PaymentMethodResource($this->repository->create($request->validated()));
    }

    public function show(PaymentMethod $paymentMethod): PaymentMethodResource
    {
        return new PaymentMethodResource($paymentMethod);
    }

    public function update(UpdatePaymentMethodRequest $request, PaymentMethod $paymentMethod): PaymentMethodResource
    {
        return new PaymentMethodResource($this->repository->update($request->validated(), $paymentMethod->id));
    }

    public function destroy(PaymentMethod $paymentMethod): Response
    {
        $this->repository->delete($paymentMethod->id);
        return response()->noContent();
    }
}
