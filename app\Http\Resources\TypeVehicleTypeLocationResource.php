<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TypeVehicleTypeLocationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_type_vehicule' => $this->id_type_vehicule,
            'id_type_location' => $this->id_type_location,
            'km_min' => $this->km_min,
            'status' => $this->status,
            'type_vehicule' => new TypeVehiculeResource($this->whenLoaded('typeVehicule')),
            'location_type' => new LocationTypeResource($this->whenLoaded('locationType')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
