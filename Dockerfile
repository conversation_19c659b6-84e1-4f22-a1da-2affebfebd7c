FROM php:8.2-fpm
# Vérifier et recréer /etc/apt/sources.list si nécessaire
RUN [ ! -f /etc/apt/sources.list ] && echo "deb https://deb.debian.org/debian bookworm main" > /etc/apt/sources.list || true \
    && echo "deb https://deb.debian.org/debian bookworm-updates main" >> /etc/apt/sources.list \
    && echo "deb https://security.debian.org/debian-security bookworm-security main" >> /etc/apt/sources.list
# Installer les dépendances PHP et APT
RUN apt-get update && apt-get install -y \
    apt-transport-https ca-certificates \
    zip unzip git curl libpng-dev libonig-dev libxml2-dev \
    libfreetype6-dev libjpeg62-turbo-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd \
    && rm -rf /var/lib/apt/lists/*
# Installer Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer
# Définir le répertoire de travail
WORKDIR /var/www/html
# Copier le projet Laravel
COPY . .
# Installer les dépendances Laravel
RUN composer install --no-interaction --prefer-dist --optimize-autoloader
# Donner les permissions nécessaires
RUN chmod -R 777 /var/www/html/storage /var/www/html/bootstrap/cache
# Lancer le serveur Laravel
CMD php artisan serve --host=0.0.0.0 --port=8000


