<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Admin;  
use App\Models\CardType;

class StockCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'id_card_type',
        'id_agent',
        'sequence_start',
        'sequence_end',
        'mouvement',
    ];
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'id_agent');
    }
    public function cardType(): BelongsTo
    {
        return $this->belongsTo(CardType::class, 'id_card_type');
    }
}
