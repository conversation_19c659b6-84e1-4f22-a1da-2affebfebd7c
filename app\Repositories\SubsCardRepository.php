<?php

namespace App\Repositories;

use App\Models\SubsCard;
use Prettus\Repository\Eloquent\BaseRepository;

class SubsCardRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'ref' => 'like',
        'id_card_type' => '=',
        'id_subscription' => '=',
        'id_motif_duplicate' => '='
    ];

    public function model(): string
    {
        return SubsCard::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}