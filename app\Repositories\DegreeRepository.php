<?php

namespace App\Repositories;

use App\Models\Degree;
use Prettus\Repository\Eloquent\BaseRepository;

class DegreeRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Degree::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'nom_fr'=> 'like',
        'nom_en'=> 'like',
        'nom_ar'=> 'like',
        'age_max' => '=',
        'id_type_establishment' => '='
    ];
}
