<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubsType extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'color',
        'is_student',
        'hasCIN',
        'is_impersonal',
        'is_conventional'
    ];

    protected $casts = [
        'is_student' => 'boolean',
        'hasCIN' => 'boolean',
        'is_impersonal' => 'boolean',
        'is_conventional' => 'boolean'
    ];

    public function tariffOptions(): HasMany
    {
        return $this->hasMany(TariffOption::class, 'id_subs_type');
    }

    public function cardFees(): HasMany
    {
        return $this->hasMany(CardFee::class, 'id_subs_type');
    }
}



