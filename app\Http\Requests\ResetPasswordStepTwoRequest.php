<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordStepTwoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'exists:subscribers,email'],
            'verificationCode' => ['required', 'string', 'size:8'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         // Email
    //         'email.required'           => [
    //             'en' => 'The email field is required.',
    //             'fr' => 'Le champ email est obligatoire.',
    //             'ar' => 'حقل البريد الإلكتروني مطلوب.',
    //         ],
    //         'email.email'              => [
    //             'en' => 'The email must be a valid email address.',
    //             'fr' => 'L’email doit être une adresse email valide.',
    //             'ar' => 'يجب أن يكون البريد الإلكتروني عنوانًا صالحًا.',
    //         ],
    //         'email.exists'             => [
    //             'en' => 'The email does not exist.',
    //             'fr' => 'Cet email n’existe pas.',
    //             'ar' => 'البريد الإلكتروني غير موجود.',
    //         ],
    //         // Verification Code
    //         'verificationCode.required' => [
    //             'en' => 'The code field is required.',
    //             'fr' => 'Le champ du code est obligatoire.',
    //             'ar' => 'حقل الرمز مطلوب.',
    //         ],
    //         'verificationCode.size'     => [
    //             'en' => 'The code must be 8 characters.',
    //             'fr' => 'Le code doit contenir 8 caractères.',
    //             'ar' => 'يجب ان يحتوي الرمز على 8 حروف.',
    //             ]
    //         ]
    //     ;
    // }
}
