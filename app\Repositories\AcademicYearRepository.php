<?php

namespace App\Repositories;

use App\Models\AcademicYear;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class AcademicYearRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'code' => 'like',
        'start_date' => '>=',
        'end_date' => '<='
    ];

    public function model(): string
    {
        return AcademicYear::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
