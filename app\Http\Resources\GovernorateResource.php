<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GovernorateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'purchase_amount' => $this->purchase_amount,
            'delegations' => DelegationResource::collection($this->whenLoaded('delegations')),
            'agencies' => AgencyResource::collection($this->whenLoaded('agencies')),
            'governorate_purchase_orders' => GovernoratePurchaseOrderResource::collection($this->whenLoaded('governoratePurchaseOrders')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

