<?php

namespace App\Repositories;

use App\Models\SocialAffair;
use Prettus\Repository\Eloquent\BaseRepository;

class SocialAffairRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return SocialAffair::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'governorate_id' => '=',
        'academic_year_id' => '=',
        'delegation' => 'like',
        'nom_parent' => 'like',
        'cin_parent' => 'like',
        'identifier' => 'like',
        'eleve_etudiant' => '=',
        'dob' => '=',
        'telephone' => 'like',
        'nom_complet' => 'like',
        'niveau_etude' => 'like'
    ];
}
