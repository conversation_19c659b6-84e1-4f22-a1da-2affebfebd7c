<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEmailStepThreeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'oldEmail' => ['required', 'email', 'exists:subscribers,email'],
            'oldEmailVerificationCode' => ['required', 'string', 'size:8'],
            'newEmail' => ['required', 'email', 'unique:subscribers,email'],
        ];
    }
}
