<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StockCardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_card_type' => $this->id_card_type,
            'id_agent' => $this->id_agent,
            'sequence_start' => $this->sequence_start,
            'sequence_end' => $this->sequence_end,
            'mouvement' => $this->mouvement,
            'cardType' => new CardTypeResource($this->whenLoaded('cardType')),
            'agent' => new AdminResource($this->whenLoaded('agent')),
        ];
    }
}
