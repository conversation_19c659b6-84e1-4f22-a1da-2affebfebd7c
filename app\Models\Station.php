<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Station extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'longitude',
        'latitude',
        'id_delegation',
        'id_governorate'
    ];

    public function delegation(): BelongsTo
    {
        return $this->belongsTo(Delegation::class, 'id_delegation');
    }

    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'id_governorate');
    }

    public function tripsAsStart(): HasMany
    {
        return $this->hasMany(Trip::class, 'id_station_start');
    }

    public function tripsAsEnd(): HasMany
    {
        return $this->hasMany(Trip::class, 'id_station_end');
    }

    public function lineStations(): HasMany
    {
        return $this->hasMany(LineStation::class, 'id_station');
    }
}


