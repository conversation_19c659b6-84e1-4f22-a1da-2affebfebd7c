<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCardFeeRequest;
use App\Http\Requests\UpdateCardFeeRequest;
use App\Http\Resources\CardFeeResource;
use App\Models\CardFee;
use App\Repositories\CardFeeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CardFeeController extends Controller
{
    private CardFeeRepository $repository;

    public function __construct(CardFeeRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(CardFee::class, 'card_fee');
    }

    public function all(): AnonymousResourceCollection
    {
        return CardFeeResource::collection(
            $this->repository->with(['subsType'])->all()
        );
    }

    public function index(): AnonymousResourceCollection
    {
        return CardFeeResource::collection(
            $this->repository
                        ->with(['subsType'])
                        ->latest()
                        ->paginate()
        );
    }

    public function store(StoreCardFeeRequest $request): JsonResponse
    {
        $cardFee = $this->repository->create($request->validated());
        
        return response()->json([
            'message' => 'Card fee created successfully',
            'data' => new CardFeeResource($cardFee->load('subsType'))
        ], 201);
    }

    public function show(CardFee $cardFee): CardFeeResource
    {
        return new CardFeeResource($cardFee->load('subsType'));
    }

    public function update(UpdateCardFeeRequest $request, CardFee $cardFee): JsonResponse
    {
        $cardFee = $this->repository->update($request->validated(), $cardFee->id);
        
        return response()->json([
            'message' => 'Card fee updated successfully',
            'data' => new CardFeeResource($cardFee->load('subsType'))
        ]);
    }

    public function destroy(CardFee $cardFee): JsonResponse
    {
        $this->repository->delete($cardFee->id);
        
        return response()->json([
            'message' => 'Card fee deleted successfully'
        ]);
    }
}