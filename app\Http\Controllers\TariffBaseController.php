<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTariffBaseRequest;
use App\Http\Requests\UpdateTariffBaseRequest;
use App\Http\Resources\TariffBaseResource;
use App\Models\TariffBase;
use App\Repositories\TariffBaseRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TariffBaseController extends Controller
{
    private TariffBaseRepository $repository;

    public function __construct(TariffBaseRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(TariffBase::class, 'tariff_base');
    }

    public function index(): AnonymousResourceCollection
    {
        return TariffBaseResource::collection($this->repository->with('subsType')->latest()->paginate());
    }

    public function all(): AnonymousResourceCollection
    {
        return TariffBaseResource::collection($this->repository->with('subsType')->findWhere(['for_website' => false]));
    }

    public function getBySubsType($subsTypeId): AnonymousResourceCollection
    {
        $tariffBases = $this->repository->with('subsType')
            ->findWhere(['id_subs_type' => $subsTypeId]);
        return TariffBaseResource::collection($tariffBases);
    }

    public function store(StoreTariffBaseRequest $request): JsonResponse
    {
        $tariffBase = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Tariff base created successfully',
            'data' => new TariffBaseResource($tariffBase)
        ], 201);
    }

    public function show(TariffBase $tariffBase): TariffBaseResource
    {
        return new TariffBaseResource($tariffBase);
    }

    public function update(UpdateTariffBaseRequest $request, TariffBase $tariffBase): JsonResponse
    {
        $tariffBase = $this->repository->update($request->validated(), $tariffBase->id);

        return response()->json([
            'message' => 'Tariff base updated successfully',
            'data' => new TariffBaseResource($tariffBase)
        ]);
    }

    public function destroy(TariffBase $tariffBase): JsonResponse
    {
        $this->repository->delete($tariffBase->id);

        return response()->json([
            'message' => 'Tariff base deleted successfully'
        ]);
    }
}

