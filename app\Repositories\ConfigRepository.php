<?php

namespace App\Repositories;

use App\Models\Config;
use Prettus\Repository\Eloquent\BaseRepository;
use Illuminate\Support\Facades\Cache;

class ConfigRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'key' => 'like',
        'group' => '=',
        'is_public' => '=',
        'is_system' => '='
    ];

    public function model(): string
    {
        return Config::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    /**
     * Get a configuration value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getValueByKey(string $key, $default = null)
    {
        return Cache::remember('config_' . $key, 60 * 60, function () use ($key, $default) {
            return Config::getValueByKey($key, $default);
        });
    }

    /**
     * Set a configuration value by key.
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @return Config
     */
    public function setValueByKey(string $key, $value, string $type = 'string')
    {
        $config = Config::setValueByKey($key, $value, $type);
        Cache::forget('config_' . $key);
        return $config;
    }

    /**
     * Get all configurations by group.
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByGroup(string $group)
    {
        return $this->scopeQuery(function ($query) use ($group) {
            return $query->where('group', $group);
        })->all();
    }

    /**
     * Get all public configurations.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPublic()
    {
        return $this->scopeQuery(function ($query) {
            return $query->where('is_public', true);
        })->all();
    }

    /**
     * Get all system configurations.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getSystem()
    {
        return $this->scopeQuery(function ($query) {
            return $query->where('is_system', true);
        })->all();
    }
}
