<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAgencyRequest;
use App\Http\Requests\UpdateAgencyRequest;
use App\Http\Resources\AgencyResource;
use App\Models\Agency;
use App\Repositories\AgencyRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class AgencyController extends Controller
{
    private AgencyRepository $repository;

    public function __construct(AgencyRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Agency::class, 'agency');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return AgencyResource::collection(
            $this->repository
                        ->with(['delegation', 'governorate'])
                        ->latest()
                        ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return AgencyResource::collection(
            $this->repository->with(['delegation', 'governorate'])->all()
        );
    }

    public function store(StoreAgencyRequest $request): JsonResponse
    {
        $agency = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Agency created successfully',
            'data' => new AgencyResource($agency->load(['delegation', 'governorate']))
        ], 201);
    }

    public function show(Agency $agency): AgencyResource
    {
        return new AgencyResource($agency->load(['delegation', 'governorate']));
    }

    public function update(UpdateAgencyRequest $request, Agency $agency): JsonResponse
    {
        $agency = $this->repository->update($request->validated(), $agency->id);

        return response()->json([
            'message' => 'Agency updated successfully',
            'data' => new AgencyResource($agency->load(['delegation', 'governorate']))
        ]);
    }

    public function destroy(Agency $agency): JsonResponse
    {
        try {
        $this->repository->delete($agency->id);

        return response()->json([
            'message' => 'Agency deleted successfully'
        ]);
    } catch (\Throwable $th) {
        return response()->json([
            'message' => 'Agency cannot be deleted, it is used in other records'
        ], 422);
    }
    }

    public function getByDelegation($delegationId): AnonymousResourceCollection
    {
        return AgencyResource::collection(
            $this->repository->with(['delegation', 'governorate'])
                ->findWhere(['id_delegation' => $delegationId])
        );
    }

    public function getByGovernorate($governorateId): AnonymousResourceCollection
    {
        return AgencyResource::collection(
            $this->repository->with(['delegation', 'governorate'])
                ->findWhere(['id_governorate' => $governorateId])
        );
    }
}
