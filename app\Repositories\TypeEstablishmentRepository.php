<?php

namespace App\Repositories;

use App\Models\TypeEstablishment;
use Prettus\Repository\Eloquent\BaseRepository;

class TypeEstablishmentRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return TypeEstablishment::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like'
    ];
}

