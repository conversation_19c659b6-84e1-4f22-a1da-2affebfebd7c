<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriberStepThreeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'unique:subscribers,email'],
            'password' => ['required', 'string', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/'],
            'confirmPassword' => ['required', 'same:password'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         // Email
    //         'email.required'           => [
    //             'en' => 'The email field is required.',
    //             'fr' => 'Le champ email est obligatoire.',
    //             'ar' => 'حقل البريد الإلكتروني مطلوب.',
    //         ],
    //         'email.email'              => [
    //             'en' => 'The email must be a valid email address.',
    //             'fr' => 'L’email doit être une adresse email valide.',
    //             'ar' => 'يجب أن يكون البريد الإلكتروني عنوانًا صالحًا.',
    //         ],
    //         'email.unique'             => [
    //             'en' => 'The email has already been taken.',
    //             'fr' => 'Cet email est déjà utilisé.',
    //             'ar' => 'البريد الإلكتروني مستخدم بالفعل.',
    //         ],

    //         // Password
    //         'password.required'        => [
    //             'en' => 'The password field is required.',
    //             'fr' => 'Le champ mot de passe est obligatoire.',
    //             'ar' => 'حقل كلمة المرور مطلوب.',
    //         ],
    //         'password.min'             => [
    //             'en' => 'The password must be at least 8 characters.',
    //             'fr' => 'Le mot de passe doit contenir au moins 8 caractères.',
    //             'ar' => 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل.',
    //         ],
    //         'password.regex'           => [
    //             'en' => 'The password must contain at least one lowercase letter, one uppercase letter, one number, and one special character (+-.!@$%^&*()).',
    //             'fr' => 'Le mot de passe doit contenir au moins une lettre minuscule, une lettre majuscule, un chiffre et un caractère spécial (+-.!@$%^&*()).',
    //             'ar' => 'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل، وحرف كبير واحد، ورقم واحد، وحرف خاص (+-.!@$%^&*()).',
    //         ],

    //         // Confirm Password
    //         'confirmPassword.required' => [
    //             'en' => 'The confirmation password field is required.',
    //             'fr' => 'Le champ de confirmation du mot de passe est obligatoire.',
    //             'ar' => 'حقل تأكيد كلمة المرور مطلوب.',
    //         ],
    //         'confirmPassword.same'     => [
    //             'en' => 'The confirmation password must match the password.',
    //             'fr' => 'La confirmation du mot de passe doit correspondre au mot de passe.',
    //             'ar' => 'يجب أن يتطابق تأكيد كلمة المرور مع كلمة المرور.',
    //         ],
    //     ];
    // }

}
