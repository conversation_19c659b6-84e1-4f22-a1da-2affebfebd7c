<?php

namespace App\Repositories;

use App\Mail\VerificationCodeStoreEmail;
use App\Models\Subscriber;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class SubscriberRepository
{
    public function model(): string
    {
        return Subscriber::class;
    }

    public function create(array $data): Subscriber
    {
        DB::table('password_reset_tokens')->where('email', $data['email'])->delete();

        return Subscriber::create([
            'lastname' => $data['lastname'],
            'firstname' => $data['firstname'],
            'phone' => $data['phone'],
            'cin' => $data['cin'],
            'address' => $data['address'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
        ]);
    }

    public function sendVerificationEmail(array $data, string $type): void
    {
        // Génerer un code de verification
        $verificationCode = rand(10000000, 99999999);
        // Envoyer le code par email
        Mail::to($data['email'])->send(new VerificationCodeStoreEmail($verificationCode, $type));
        // Hasher le code avant de l’enregistrer
        $hashedCode = Hash::make($verificationCode);
        // Mettre le code de verification dans la base de données
        DB::table('password_reset_tokens')->updateOrInsert([
            'email' => $data['email'],
        ], [
            'email' => $data['email'],
            'token' => $hashedCode,
            'created_at' => now()
        ]);
    }

    public function verifyCode(string $email, string $verificationCode): bool
    {
        $record = DB::table('password_reset_tokens')->where('email', $email)->first();

        if (!$record) {
            return false; // Pas de code trouvé pour cet email
        }

        // Vérifie si le code correspond
        if (! Hash::check($verificationCode, $record->token)) {
            return false; // Le code ne correspond pas
        }

        // Vérifie si le code est encore valide (moins de 10 minutes)
        $createdAt = \Carbon\Carbon::parse($record->created_at);
        if (now()->diffInMinutes($createdAt) > 10) {
            return false;
        }

        return true;
    }

    public function resetPassword(array $data): void
    {
        DB::table('password_reset_tokens')->where('email', $data['email'])->delete();

        Subscriber::where('email', $data['email'])->update([
            'password' => bcrypt($data['password']),
        ]);
    }

    public function updateProfile(array $data): void
    {
        $subscriber = auth()->user();
        Subscriber::where('id', $subscriber->id)->update([
            'lastname' => $data['lastname'],
            'firstname' => $data['firstname'],
            'phone' => $data['phone'],
            'cin' => $data['cin'],
            'address' => $data['address'],
        ]);
    }

    public function UpdatePassword(array $data): void
    {
        $subscriber = auth()->user();
        Subscriber::where('id', $subscriber->id)->update([
            'password' => bcrypt($data['password']),
        ]);
    }

    public function UpdateEmail(array $data): void
    {
        $subscriber = auth()->user();
        Subscriber::where('id', $subscriber->id)->update([
            'email' => $data['newEmail'],
        ]);
    }
}
