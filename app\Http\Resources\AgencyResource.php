<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AgencyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'code' => $this->code,
            'contact' => $this->contact,
            'address' => $this->address,
            'id_delegation' => $this->id_delegation,
            'id_governorate' => $this->id_governorate,
            'delegation' => new DelegationResource($this->whenLoaded('delegation')),
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

