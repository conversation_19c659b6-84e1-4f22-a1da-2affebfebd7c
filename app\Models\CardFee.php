<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CardFee extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'amount',
        'id_subs_type'
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }
}
