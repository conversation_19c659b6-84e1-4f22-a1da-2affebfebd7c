<?php

namespace App\Repositories;

use App\Models\Trip;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TripRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'id_line' => '=',
        'id_station_start' => '=',
        'id_station_end' => '=',
        'status' => '=',
        'number_of_km' => '='
    ];

    public function model(): string
    {
        return Trip::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    public function create(array $data): Trip
    {
        try {
            DB::beginTransaction();

            // Prepare trip data
            $tripData = [
                'nom_fr' => $data['nom_fr'],
                'nom_en' => $data['nom_en'],
                'nom_ar' => $data['nom_ar'],
                'id_line' => $data['id_line'],
                'id_station_start' => $data['stations']['id_station_start'],
                'id_station_end' => $data['stations']['id_station_end'],
                'status' => $data['status'],
                'inter_station' => $data['inter_station'],
                'number_of_km' => $data['number_of_km']
            ];

            // Create trip
            $trip = parent::create($tripData);

            // Create tariff options
            if (!empty($data['tariff_options'])) {
                foreach ($data['tariff_options'] as $option) {
                    $tariffData = [
                        'id_trip' => $trip->id,
                        'id_subs_type' => $option['id_subs_type'],
                        'is_regular' => $option['is_regular']
                    ];

                    if ($option['is_regular']) {
                        $tariffData['id_tariff_base'] = $option['id_tariff_base'];
                    } else {
                        $tariffData['manual_tariff'] = $option['manual_tariff'];
                    }

                    $trip->tariffOptions()->create($tariffData);
                }
            }

            DB::commit();

            return $trip->load(['line', 'startStation', 'endStation', 'tariffOptions']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, $id): Trip
    {
        try {
            DB::beginTransaction();

            $trip = $this->find($id);

            $tripData = array_filter([
                'nom_fr' => $data['nom_fr'] ?? null,
                'nom_en' => $data['nom_en'] ?? null,
                'nom_ar' => $data['nom_ar'] ?? null,
                'id_line' => $data['id_line'] ?? null,
                'status' => $data['status'] ?? null,
                'inter_station' => $data['inter_station'] ?? null,
                'number_of_km' => $data['number_of_km'] ?? null,
                'id_station_start' => $data['stations']['id_station_start'] ?? null,
                'id_station_end' => $data['stations']['id_station_end'] ?? null,
            ], function($value) {
                return !is_null($value);
            });

            $trip = parent::update($tripData, $id);

            if (isset($data['tariff_options'])) {
                $trip->tariffOptions()->delete();

                foreach ($data['tariff_options'] as $option) {
                    $tariffData = [
                        'id_trip' => $trip->id,
                        'id_subs_type' => $option['id_subs_type'],
                        'is_regular' => $option['is_regular']
                    ];

                    if ($option['is_regular']) {
                        $tariffData['id_tariff_base'] = $option['id_tariff_base'];
                    } else {
                        $tariffData['manual_tariff'] = $option['manual_tariff'];
                    }

                    $trip->tariffOptions()->create($tariffData);
                }
            }

            DB::commit();
            return $trip->fresh()->load(['line', 'startStation', 'endStation', 'tariffOptions']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete($id): bool
    {
        try {
            DB::beginTransaction();

            $trip = $this->find($id);

            // Delete related tariff options first
            $trip->tariffOptions()->delete();

            // Delete the trip
            $result = parent::delete($id);

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}



