<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTypeEstablishmentRequest;
use App\Http\Requests\UpdateTypeEstablishmentRequest;
use App\Http\Resources\TypeEstablishmentResource;
use App\Models\TypeEstablishment;
use App\Repositories\TypeEstablishmentRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TypeEstablishmentController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(TypeEstablishmentRepository::class);
        $this->authorizeResource(TypeEstablishment::class, 'type_establishment');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $typeEstablishments = $this->repository->latest()->paginate();
        return TypeEstablishmentResource::collection($typeEstablishments);
    }

    public function all(): AnonymousResourceCollection
    {
        $typeEstablishments = $this->repository->all();
        return TypeEstablishmentResource::collection($typeEstablishments);
    }

    public function store(StoreTypeEstablishmentRequest $request): JsonResponse
    {
        $typeEstablishment = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Type establishment created successfully',
            'data' => new TypeEstablishmentResource($typeEstablishment)
        ], 201);
    }

    public function show(TypeEstablishment $typeEstablishment): TypeEstablishmentResource
    {
        return new TypeEstablishmentResource($typeEstablishment);
    }

    public function update(UpdateTypeEstablishmentRequest $request, TypeEstablishment $typeEstablishment): JsonResponse
    {
        $typeEstablishment = $this->repository->update($request->validated(), $typeEstablishment->id);

        return response()->json([
            'message' => 'Type establishment updated successfully',
            'data' => new TypeEstablishmentResource($typeEstablishment)
        ]);
    }

    public function destroy(TypeEstablishment $typeEstablishment): JsonResponse
    {
        $this->repository->delete($typeEstablishment->id);
        return response()->json([
            'message' => 'Type establishment deleted successfully'
        ]);
    }
}
