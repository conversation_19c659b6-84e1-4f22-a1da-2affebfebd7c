<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TypeVehicule extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'code',
        'nbre_max_place',
        'swf',
        'photo',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
        'nbre_max_place' => 'integer'
    ];

    public function typeVehicleTypeLocations(): HasMany
    {
        return $this->hasMany(TypeVehicleTypeLocation::class, 'id_type_vehicule');
    }

    public function typeVehiculeSaisonLocations(): HasMany
    {
        return $this->hasMany(TypeVehiculeSaisonLocation::class, 'id_type_vehicule');
    }
}
