<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebsiteTripResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'id_line' => $this->id_line,
            'line' => new LineResource($this->whenLoaded('line')),
            'id_station_start' => $this->id_station_start,
            'start_station' => new StationResource($this->whenLoaded('startStation')),
            'id_station_end' => $this->id_station_end,
            'end_station' => new StationResource($this->whenLoaded('endStation')),
            'status' => $this->status,
            'number_of_km' => $this->number_of_km,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
