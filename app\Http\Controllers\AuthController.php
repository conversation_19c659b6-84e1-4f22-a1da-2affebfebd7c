<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * Get a JWT via given credentials.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'email',
            'cin' => 'size:8',
            'phone' => 'size:8',
            'password' => 'required',
            'captcha' => 'required',
            'generatedCaptcha' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation failed','errors' => $validator->errors()], 422);
        }

        $captchaFromRequest = strtolower($request->input('captcha'));
        $hashedCaptchaText = $request->input('generatedCaptcha');

        if (!Hash::check($captchaFromRequest, $hashedCaptchaText)) {
            return response()->json(['message' => 'Captcha invalide'], 400);
        }

        $loginField = $request->input('email') ? 'email' : ($request->input('cin') ? 'cin' : 'phone');
        $credentials = request([$loginField, 'password']);

        if (!$token = auth()->attempt($credentials)) {
            return response()->json(['message' => 'Credentials incorrect'], 401);
        }

        $customClaims = [
            'user' => Auth::guard('api')->user()->withoutRelations()
        ];

        $token = Auth::guard('api')->claims($customClaims)->attempt($credentials);
        return response()->json([
            'status' => 'success',
            'message' => 'Authentification réussie',
            'token' => $token
        ]);
    }

    /**
     * Get the authenticated User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function me()
    {
        return response()->json(auth()->user());
    }

    /**
     * Log the user out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth()->logout();

        return response()->json(['message' => 'Successfully logged out']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        return $this->respondWithToken(auth()->refresh());
    }

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 60
        ]);
    }
}
