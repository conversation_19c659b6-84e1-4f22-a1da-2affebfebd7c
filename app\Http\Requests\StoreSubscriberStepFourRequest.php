<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriberStepFourRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'unique:subscribers,email'],
            'verificationCode' => ['required', 'string', 'size:8'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         // Email
    //         'email.required'           => [
    //             'en' => 'The email field is required.',
    //             'fr' => 'Le champ email est obligatoire.',
    //             'ar' => 'حقل البريد الإلكتروني مطلوب.',
    //         ],
    //         'email.email'              => [
    //             'en' => 'The email must be a valid email address.',
    //             'fr' => 'L’email doit être une adresse email valide.',
    //             'ar' => 'يجب أن يكون البريد الإلكتروني عنوانًا صالحًا.',
    //         ],
    //         'email.unique'             => [
    //             'en' => 'The email has already been taken.',
    //             'fr' => 'Cet email est déjà utilisé.',
    //             'ar' => 'البريد الإلكتروني مستخدم بالفعل.',
    //         ],

    //         // Verification Code
    //         'verificationCode.required' => [
    //             'en' => 'The code field is required.',
    //             'fr' => 'Le champ du code est obligatoire.',
    //             'ar' => 'حقل الرمز مطلوب.',
    //         ],
    //         'verificationCode.size'     => [
    //             'en' => 'The size of code must be 8.',
    //             'fr' => 'Le code doit comporter 8 caractères.',
    //             'ar' => 'يجب أن يكون طول الرمز 8 أحرف.',
    //         ],
    //     ];
    // }
}
