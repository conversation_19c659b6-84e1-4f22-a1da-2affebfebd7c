<?php

namespace App\Http\Controllers;

use App\Http\Resources\CardSequenceResource;
use App\Models\CardSequence;
use App\Models\CardType;
use App\Services\CardSequenceTracker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class CardSequenceController extends Controller
{
    protected $cardSequenceTracker;

    public function __construct(CardSequenceTracker $cardSequenceTracker)
    {
        $this->cardSequenceTracker = $cardSequenceTracker;
    }

    /**
     * Display a listing of the card sequences.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = CardSequence::with(['cardType', 'agent']);

        // Filter by card type if provided
        if ($request->has('id_card_type')) {
            $query->where('id_card_type', $request->id_card_type);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by agent if provided
        if ($request->has('id_agent')) {
            $query->where('id_agent', $request->id_agent);
        }

        return CardSequenceResource::collection(
            $query->latest()->paginate($request->input('perPage'))
        );
    }

    /**
     * Get all available sequences for a specific card type.
     */
    public function getAvailableSequences(int $cardTypeId): AnonymousResourceCollection
    {
        return CardSequenceResource::collection(
            CardSequence::with(['cardType', 'agent'])
                ->where('id_card_type', $cardTypeId)
                ->where('status', 'available')
                ->orderBy('start_sequence')
                ->get()
        );
    }

    /**
     * Get all occupied sequences for a specific card type.
     */
    public function getOccupiedSequences(int $cardTypeId): AnonymousResourceCollection
    {
        return CardSequenceResource::collection(
            CardSequence::with(['cardType', 'agent'])
                ->where('id_card_type', $cardTypeId)
                ->where('status', 'occupied')
                ->orderBy('start_sequence')
                ->get()
        );
    }

    /**
     * Get a summary of available and occupied sequences for all card types.
     */
    public function getSummary(): JsonResponse
    {
        $cardTypes = CardType::all();
        $summary = [];

        foreach ($cardTypes as $cardType) {
            $availableCount = CardSequence::where('id_card_type', $cardType->id)
                ->where('status', 'available')
                ->sum(DB::raw('end_sequence - start_sequence + 1'));

            $occupiedCount = CardSequence::where('id_card_type', $cardType->id)
                ->where('status', 'occupied')
                ->sum(DB::raw('end_sequence - start_sequence + 1'));

            $summary[] = [
                'id' => $cardType->id,
                'name' => $cardType->nom_fr,
                'code' => $cardType->code,
                'available_count' => $availableCount,
                'occupied_count' => $occupiedCount,
                'total_count' => $availableCount + $occupiedCount,
            ];
        }

        return response()->json([
            'data' => $summary
        ]);
    }
}
