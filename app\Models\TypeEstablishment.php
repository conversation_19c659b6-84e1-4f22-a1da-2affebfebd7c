<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TypeEstablishment extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar'
    ];

    public function establishments(): HasMany
    {
        return $this->hasMany(Establishment::class, 'id_type_establishment');
    }

    public function degrees(): HasMany
    {
        return $this->hasMany(Degree::class, 'id_type_establishment');
    }
}

