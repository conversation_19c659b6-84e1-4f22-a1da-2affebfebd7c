<?php

namespace App\Http\Controllers;

use App\Models\Periodicity;
use App\Repositories\PeriodicityRepository;
use App\Http\Requests\StorePeriodicityRequest;
use App\Http\Requests\UpdatePeriodicityRequest;
use App\Http\Resources\PeriodicityResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PeriodicityController extends Controller
{
    private PeriodicityRepository $repository;

    public function __construct(PeriodicityRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Periodicity::class, 'periodicity');
    }

    public function index(): AnonymousResourceCollection
    {
        return PeriodicityResource::collection($this->repository->latest()->paginate());
    }

    public function all(): AnonymousResourceCollection
    {
        return PeriodicityResource::collection($this->repository->all());
    }

    public function store(StorePeriodicityRequest $request): JsonResponse
    {
        $periodicity = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Periodicity created successfully',
            'data' => new PeriodicityResource($periodicity)
        ], 201);
    }

    public function show(Periodicity $periodicity): PeriodicityResource
    {
        return new PeriodicityResource($periodicity);
    }

    public function update(UpdatePeriodicityRequest $request, Periodicity $periodicity): JsonResponse
    {
        $periodicity = $this->repository->update($request->validated(), $periodicity->id);

        return response()->json([
            'message' => 'Periodicity updated successfully',
            'data' => new PeriodicityResource($periodicity)
        ]);
    }

    public function destroy(Periodicity $periodicity): JsonResponse
    {
        $this->repository->delete($periodicity->id);

        return response()->json([
            'message' => 'Periodicity deleted successfully'
        ]);
    }
}
