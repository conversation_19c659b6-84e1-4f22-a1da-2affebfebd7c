<?php

namespace App\Repositories;

use Spatie\Permission\Models\Role;
use Prettus\Repository\Eloquent\BaseRepository;

class RoleRepository extends BaseRepository
{
    protected $cacheKey = '';
    public function model(): string
    {
        return Role::class;
    }
    public function boot(){
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
    protected $fieldSearchable = [
        'name'=> 'like',
    ];
}
