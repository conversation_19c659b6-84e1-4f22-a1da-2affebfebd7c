<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreClientRequest;
use App\Http\Requests\UpdateClientRequest;
use App\Http\Resources\ClientResource;
use App\Models\Client;
use App\Repositories\ClientRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class ClientController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(ClientRepository::class);
        $this->authorizeResource(Client::class, 'client');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $clients = $this->repository->with([
            'clientType',
            'delegation',
            'governorate',
            'establishment',
            'degree'
        ])
            ->latest()
            ->paginate($request->input('perPage'));
        return ClientResource::collection($clients);
    }

    public function store(StoreClientRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['password'] = Hash::make($data['password']);

        $client = $this->repository->create($data);
        $client->load(['delegation', 'governorate', 'establishment', 'degree']);

        return response()->json([
            'message' => 'Client created successfully',
            'data' => new ClientResource($client)
        ], 201);
    }

    public function show(Client $client): ClientResource
    {
        $client->load([
            'clientType',
            'delegation',
            'governorate',
            'establishment',
            'degree'
        ]);
        return new ClientResource($client);
    }

    public function update(UpdateClientRequest $request, Client $client): JsonResponse
    {
        $data = $request->validated();
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $client = $this->repository->update($data, $client->id);
        $client->load(['clientType', 'delegation', 'governorate']);

        return response()->json([
            'message' => 'Client updated successfully',
            'data' => new ClientResource($client)
        ]);
    }

    public function destroy(Client $client): JsonResponse
    {
        $this->repository->delete($client->id);
        return response()->json([
            'message' => 'Client deleted successfully'
        ]);
    }

    public function getByDelegation($delegationId): AnonymousResourceCollection
    {
        $clients = $this->repository->with(['clientType', 'delegation', 'governorate'])
            ->findWhere(['id_delegation' => $delegationId]);
        return ClientResource::collection($clients);
    }

    public function getByGovernorate($governorateId): AnonymousResourceCollection
    {
        $clients = $this->repository->with(['clientType', 'delegation', 'governorate'])
            ->findWhere(['id_governorate' => $governorateId]);
        return ClientResource::collection($clients);
    }

    public function getByClientType($clientTypeId): AnonymousResourceCollection
    {
        $clients = $this->repository->with(['clientType', 'delegation', 'governorate'])
            ->findWhere(['id_client_type' => $clientTypeId]);
        return ClientResource::collection($clients);
    }


    public function searchStudent(Request $request): JsonResponse
    {
        $request->validate([
            'dob' => 'required|date',
            'identity_number' => 'required|string'
        ]);

        $client = $this->repository
            ->with([
                'clientType',
                'delegation',
                'governorate',
                'establishment',
                'establishment.delegation',
                'degree'
            ])
            ->scopeQuery(function($query) use ($request) {
                return $query->whereDate('dob', Carbon::parse($request->dob)->format('Y-m-d'))
                            ->where('identity_number', $request->identity_number)
                            ->whereHas('clientType', function($query) {
                                $query->where('is_student', true)
                                     ->where('hasCIN', false);
                            });
            })
            ->first();

        if (!$client) {
            return response()->json([
                'message' => trans('messages.client.has_cin.not_found')
            ], 404);
        }

        return response()->json([
            'message' => 'Student found successfully',
            'data' => new ClientResource($client)
        ]);
    }

    public function getClientsHasCIN(): AnonymousResourceCollection
    {
        $clients = $this->repository
            ->with([
                'clientType',
                'delegation',
                'governorate',
                'establishment',
                'degree'
            ])
            ->scopeQuery(function($query) {
                return $query->whereHas('clientType', function($query) {
                    $query->where('hasCIN', true);
                });
            })
            ->all();

        return ClientResource::collection($clients);
    }

    public function getImpersonalClients(): AnonymousResourceCollection
    {
        $clients = $this->repository
            ->with([
                'clientType',
                'delegation',
                'governorate',
                'establishment',
                'degree'
            ])
            ->scopeQuery(function($query) {
                return $query->whereHas('clientType', function($query) {
                    $query->where('is_impersonal', true);
                });
            })
            ->all();

        return ClientResource::collection($clients);
    }

    public function getConventionalClients(): AnonymousResourceCollection
    {
        $clients = $this->repository
            ->with([
                'clientType',
                'delegation',
                'governorate',
                'establishment',
                'degree'
            ])
            ->scopeQuery(function($query) {
                return $query->whereHas('clientType', function($query) {
                    $query->where('is_conventional', true);
                });
            })
            ->all();

        return ClientResource::collection($clients);
    }
}



