<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TariffOption extends Model
{
    protected $fillable = [
        'id_trip',
        'id_subs_type',
        'is_regular',
        'id_tariff_base',
        'manual_tariff'
    ];

    protected $casts = [
        'is_regular' => 'boolean',
        'manual_tariff' => 'integer'
    ];

    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class, 'id_trip');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }

    public function tariffBase(): BelongsTo
    {
        return $this->belongsTo(TariffBase::class, 'id_tariff_base');
    }
}