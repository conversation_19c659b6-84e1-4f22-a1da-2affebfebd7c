<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VerifCitoyenFamilleResquest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
         return [
            "idEdu"=>"required|digits:12",
            "jourNaiss"=>"required|digits:2",
            "moisNaiss"=>"required|digits:2",
            "anneeNaiss"=>"required|digits:4"
        ];
    }
}
