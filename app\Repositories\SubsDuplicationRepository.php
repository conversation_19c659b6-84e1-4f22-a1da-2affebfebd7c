<?php

namespace App\Repositories;


use App\Models\SubsDuplication;
use Prettus\Repository\Eloquent\BaseRepository;

class SubsDuplicationRepository extends BaseRepository
{
    protected $fieldSearchable = [
        
    ];

    public function model(): string
    {
        return SubsDuplication::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));

    }
}