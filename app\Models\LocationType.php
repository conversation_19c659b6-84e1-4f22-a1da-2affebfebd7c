<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LocationType extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'code',
        'documents',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    public function typeVehicleTypeLocations(): HasMany
    {
        return $this->hasMany(TypeVehicleTypeLocation::class, 'id_type_location');
    }
}