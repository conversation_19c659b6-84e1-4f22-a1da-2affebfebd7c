<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreConfigRequest;
use App\Http\Requests\UpdateConfigRequest;
use App\Http\Resources\ConfigResource;
use App\Models\Config;
use App\Repositories\ConfigRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ConfigController extends Controller
{
    private ConfigRepository $repository;

    public function __construct(ConfigRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Config::class, 'config');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return ConfigResource::collection(
            $this->repository
                ->latest()
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return ConfigResource::collection(
            $this->repository->all()
        );
    }

    public function store(StoreConfigRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Traiter la valeur en fonction du type
            if (isset($data['value']) && isset($data['type'])) {
                switch ($data['type']) {
                    case 'boolean':
                        $data['value'] = filter_var($data['value'], FILTER_VALIDATE_BOOLEAN) ? '1' : '0';
                        break;
                    case 'json':
                        if (is_array($data['value'])) {
                            $data['value'] = json_encode($data['value']);
                        }
                        break;
                    case 'array':
                        if (is_array($data['value'])) {
                            $data['value'] = implode(',', $data['value']);
                        }
                        break;
                }
            }
            
            $config = $this->repository->create($data);
            
            // Vider le cache pour cette clé
            Cache::forget('config_' . $config->key);
            
            return response()->json([
                'message' => 'Configuration created successfully',
                'data' => new ConfigResource($config)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Config $config): ConfigResource
    {
        return new ConfigResource($config);
    }

    public function update(UpdateConfigRequest $request, Config $config): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Traiter la valeur en fonction du type
            if (isset($data['value']) && isset($data['type'])) {
                switch ($data['type']) {
                    case 'boolean':
                        $data['value'] = filter_var($data['value'], FILTER_VALIDATE_BOOLEAN) ? '1' : '0';
                        break;
                    case 'json':
                        if (is_array($data['value'])) {
                            $data['value'] = json_encode($data['value']);
                        }
                        break;
                    case 'array':
                        if (is_array($data['value'])) {
                            $data['value'] = implode(',', $data['value']);
                        }
                        break;
                }
            }
            
            $config = $this->repository->update($data, $config->id);
            
            // Vider le cache pour cette clé
            Cache::forget('config_' . $config->key);
            
            return response()->json([
                'message' => 'Configuration updated successfully',
                'data' => new ConfigResource($config)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Config $config): JsonResponse
    {
        try {
            // Vider le cache pour cette clé
            Cache::forget('config_' . $config->key);
            
            $this->repository->delete($config->id);
            
            return response()->json([
                'message' => 'Configuration deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getByGroup(string $group): AnonymousResourceCollection
    {
        return ConfigResource::collection(
            $this->repository->getByGroup($group)
        );
    }

    public function getPublic(): AnonymousResourceCollection
    {
        return ConfigResource::collection(
            $this->repository->getPublic()
        );
    }

    public function getSystem(): AnonymousResourceCollection
    {
        return ConfigResource::collection(
            $this->repository->getSystem()
        );
    }

    public function getByKey(string $key): JsonResponse
    {
        $value = $this->repository->getValueByKey($key);
        
        if ($value === null) {
            return response()->json([
                'message' => 'Configuration not found'
            ], 404);
        }
        
        return response()->json([
            'key' => $key,
            'value' => $value
        ]);
    }

    public function setByKey(Request $request, string $key): JsonResponse
    {
        try {
            $request->validate([
                'value' => 'required',
                'type' => 'required|string|in:string,integer,float,boolean,json,array'
            ]);
            
            $value = $request->input('value');
            $type = $request->input('type');
            
            $config = $this->repository->setValueByKey($key, $value, $type);
            
            return response()->json([
                'message' => 'Configuration updated successfully',
                'data' => new ConfigResource($config)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
