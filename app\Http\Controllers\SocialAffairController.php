<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportSocialAffairRequest;
use App\Http\Requests\StoreSocialAffairRequest;
use App\Http\Requests\UpdateSocialAffairRequest;
use App\Http\Resources\SocialAffairResource;
use App\Imports\SocialAffairsImport;
use App\Models\SocialAffair;
use App\Repositories\SocialAffairRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Maatwebsite\Excel\Facades\Excel;

class SocialAffairController extends Controller
{
    private SocialAffairRepository $repository;

    public function __construct(SocialAffairRepository $repository)
    {
        $this->repository = $repository;
        //$this->authorizeResource(SocialAffair::class, 'is_social_affair');
    }

    /**
     * Verify social affair.
     */
    public function verify(Request $request): JsonResponse
    {
        $this->authorize('verify', SocialAffair::class);
        $dob = $request->input('dob');
        $identifier = $request->input('identifier');
        $eleve_etudiant = $request->input('eleve_etudiant');

        if (!$dob || !$identifier || !$eleve_etudiant) {
            return response()->json([
                'status' => 'error',
                'message' => 'dob, identifier, and eleve_etudiant are required',
                'exists' => false,
                'has_purchase_order' => false
            ], 400);
        }
        $socialAffair = $this->repository->with('governorate')->findWhere([
            'eleve_etudiant' => $eleve_etudiant,
            'identifier' => $identifier,
            'dob' => $dob
        ])->first();
        $exists = $socialAffair !== null;

        if (!$exists) {
            return response()->json([
                'status' => 'not_found',
                'exists' => false,
                'has_purchase_order' => false
            ]);
        }

        $governorateId = $socialAffair->governorate_id;
        $purchaseOrderRepository = resolve(\App\Repositories\GovernoratePurchaseOrderRepository::class);

        $purchaseOrders = $purchaseOrderRepository->findWhere([
            'id_governorate' => $governorateId,
            'status' => true
        ]);

        $hasAvailableAmount = $purchaseOrders->sum('current_amount') > 0;

        if (!$hasAvailableAmount) {
            return response()->json([
                'status' => 'no_purchase_order',
                'exists' => true,
                'has_purchase_order' => false,
                'user_data' => [
                    'governorate_id' => $governorateId,
                    'governorate_name' => $socialAffair->governorate->nom_fr,
                    'nom_complet' => $socialAffair->nom_complet,
                    'eleve_etudiant' => $socialAffair->eleve_etudiant
                ]
            ]);
        }

        return response()->json([
            'status' => 'success',
            'exists' => true,
            'has_purchase_order' => true,
            'user_data' => [
                'governorate_id' => $governorateId,
                'governorate_name' => $socialAffair->governorate->nom_fr,
                'nom_complet' => $socialAffair->nom_complet,
                'eleve_etudiant' => $socialAffair->eleve_etudiant
            ],
            'purchase_orders' => [
                'total_amount' => $purchaseOrders->sum('current_amount'),
                'count' => $purchaseOrders->count()
            ]
        ]);
    }


    /**
     * Verify social affair with cin parent.
     */
    public function verifyCinParent(Request $request): JsonResponse
    {
        //$this->authorize('verify', SocialAffair::class);

        $cin_parent = $request->input('cin_parent');
        $final_amount = $request->input('final_amount');

        if (!$cin_parent) {
            return response()->json([
                'status' => 'error',
                'message' => 'cin parent are required',
                'exists' => false,
                'has_purchase_order' => false
            ], 400);
        }
        $socialAffair = $this->repository->with('governorate')->findWhere([
            'cin_parent' => $cin_parent,
        ])->first();
        $exists = $socialAffair !== null;

        if (!$exists) {
            return response()->json([
                'social_affair' => $socialAffair,
                'status' => 'not_found',
                'exists' => false,
                'has_purchase_order' => false
            ]);
        }

        $hasAvailableAmount = $socialAffair->governorate->purchase_amount > $final_amount;

        if (!$hasAvailableAmount) {
            return response()->json([
                'status' => 'no_purchase_order',
                'exists' => true,
                'has_purchase_order' => false,
                'governorate_data' => $socialAffair->governorate,
            ]);
        }

        return response()->json([
            'status' => 'success',
            'exists' => true,
            'has_purchase_order' => true,
            'governorate_data' => $socialAffair->governorate,
        ]);
    }
}
