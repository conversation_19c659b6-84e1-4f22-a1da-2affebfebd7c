<?php

namespace App\Repositories;

use App\Models\StockCard;
use Prettus\Repository\Eloquent\BaseRepository;

/**
 * Interface StockCardRepositoryRepository.
 *
 * @package namespace App\Repositories;
 */
class StockCardRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return StockCard::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'id_card_type' => '=',
        'id_agent' => '=',
        'sequence_start' => '=',
        'sequence_end' => '=',
        'mouvement' => 'like'
    ];
    public function getLatestRecord($id_card_type)
    {
        return $this->model
            ->where('id_card_type', $id_card_type)
            ->where('mouvement', 'ajout')
            ->latest()
            ->first();
    }
    public function findRecord($id_card_type, $seqDebut)
    {
        return $this->model
            ->where('id_card_type', $id_card_type)
            ->where('sequence_start', '<=', $seqDebut)
            ->where('sequence_end', '>=', $seqDebut)
            ->first();
    }

}
