<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDelegationRequest;
use App\Http\Requests\UpdateDelegationRequest;
use App\Http\Resources\DelegationResource;
use App\Models\Delegation;
use App\Repositories\DelegationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DelegationController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(DelegationRepository::class);
        $this->authorizeResource(Delegation::class, 'delegation');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $delegations = $this->repository
                        ->with(['governorate'])
                        ->latest()
                        ->paginate($request->input('perPage'));
        return DelegationResource::collection($delegations);
    }

    public function all(): AnonymousResourceCollection
    {
        $delegations = $this->repository->with(['governorate'])->all();
        return DelegationResource::collection($delegations);
    }

    public function store(StoreDelegationRequest $request): JsonResponse
    {
        $delegation = $this->repository->create($request->validated());
        $delegation->load('governorate');

        return response()->json([
            'message' => 'Delegation created successfully',
            'data' => new DelegationResource($delegation)
        ], 201);
    }

    public function show(Delegation $delegation): DelegationResource
    {
        $delegation->load('governorate');
        return new DelegationResource($delegation);
    }

    public function update(UpdateDelegationRequest $request, Delegation $delegation): JsonResponse
    {
        $delegation = $this->repository->update($request->validated(), $delegation->id);
        $delegation->load('governorate');

        return response()->json([
            'message' => 'Delegation updated successfully',
            'data' => new DelegationResource($delegation)
        ]);
    }

    public function destroy(Delegation $delegation): JsonResponse
    {
        $this->repository->delete($delegation->id);
        return response()->json([
            'message' => 'Delegation deleted successfully'
        ]);
    }

    public function getByGovernorate($governorateId): AnonymousResourceCollection
    {
        $delegations = $this->repository->with(['governorate'])
            ->findWhere(['id_governorate' => $governorateId]);
        return DelegationResource::collection($delegations);
    }
}
