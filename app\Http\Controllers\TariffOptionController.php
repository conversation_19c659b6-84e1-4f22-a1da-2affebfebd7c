<?php

namespace App\Http\Controllers;

use App\Models\TariffOption;
use App\Repositories\TariffOptionRepository;
use App\Http\Requests\StoreTariffOptionRequest;
use App\Http\Requests\UpdateTariffOptionRequest;
use App\Http\Resources\TariffOptionResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TariffOptionController extends Controller
{
    private TariffOptionRepository $repository;

    public function __construct(TariffOptionRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(TariffOption::class, 'tariff_option');
    }

    public function index(): AnonymousResourceCollection
    {
        return TariffOptionResource::collection(
            $this->repository->with(['trip', 'subsType', 'tariffBase'])->latest()->paginate()
        );
    }

    public function store(StoreTariffOptionRequest $request): JsonResponse
    {
        $tariffOption = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Tariff option created successfully',
            'data' => new TariffOptionResource(
                $tariffOption->load(['trip', 'subsType', 'tariffBase'])
            )
        ], 201);
    }

    public function show(TariffOption $tariffOption): TariffOptionResource
    {
        return new TariffOptionResource(
            $tariffOption->load(['trip', 'subsType', 'tariffBase'])
        );
    }

    public function update(UpdateTariffOptionRequest $request, TariffOption $tariffOption): JsonResponse
    {
        $tariffOption = $this->repository->update($request->validated(), $tariffOption->id);

        return response()->json([
            'message' => 'Tariff option updated successfully',
            'data' => new TariffOptionResource(
                $tariffOption->load(['trip', 'subsType', 'tariffBase'])
            )
        ]);
    }

    public function destroy(TariffOption $tariffOption): JsonResponse
    {
        $this->repository->delete($tariffOption->id);

        return response()->json([
            'message' => 'Tariff option deleted successfully'
        ]);
    }
}
