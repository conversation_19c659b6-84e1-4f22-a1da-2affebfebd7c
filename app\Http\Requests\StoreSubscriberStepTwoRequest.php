<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriberStepTwoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cin' => ['required', 'string', 'size:8', 'unique:subscribers,cin'],
            'address' => ['required', 'string', 'max:255'],
        ];
    }

//     public function messages(): array
// {
//     return [
//         // CIN
//         'cin.required'  => [
//             'en' => 'The CIN field is required.',
//             'fr' => 'Le champ CIN est obligatoire.',
//             'ar' => 'حقل بطاقة التعريف مطلوب.',
//         ],
//         'cin.size'      => [
//             'en' => 'The size of CIN must be 8.',
//             'fr' => 'La taille de la CIN doit être de 8 caractères.',
//             'ar' => 'يجب أن يكون طول رقم التعريف 8 أحرف.',
//         ],
//         'cin.unique'    => [
//             'en' => 'The CIN has already been taken.',
//             'fr' => 'Cette CIN est déjà utilisée.',
//             'ar' => 'رقم التعريف مستخدم بالفعل.',
//         ],

//         // Address
//         'address.required' => [
//             'en' => 'The address field is required.',
//             'fr' => 'Le champ adresse est obligatoire.',
//             'ar' => 'حقل العنوان مطلوب.',
//         ],
//         'address.max'      => [
//             'en' => 'The address must not exceed 255 characters.',
//             'fr' => 'L’adresse ne doit pas dépasser 255 caractères.',
//             'ar' => 'يجب ألا يتجاوز العنوان 255 حرفًا.',
//         ],
//     ];
// }

}
