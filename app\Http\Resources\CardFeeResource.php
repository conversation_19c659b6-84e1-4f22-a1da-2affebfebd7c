<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CardFeeResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'amount' => $this->amount,
            'id_subs_type' => $this->id_subs_type,
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}