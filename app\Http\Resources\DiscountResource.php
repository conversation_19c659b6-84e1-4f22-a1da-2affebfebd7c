<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DiscountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'percentage' => $this->percentage,
            'is_stagiaire' => $this->is_stagiaire,
            'special_client' => $this->special_client,
            'date_start' => $this->date_start,
            'date_end' => $this->date_end,
            'periodicities' => PeriodicityResource::collection($this->whenLoaded('periodicities')),
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

