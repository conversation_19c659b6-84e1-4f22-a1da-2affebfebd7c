<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\VerifCitoyenFamilleResquest;
use App\Http\Requests\VerifCitoyenResquest;
use App\Http\Requests\VerifCivilRequest;
use App\Http\Requests\VerifEleveRequest;
use App\Http\Requests\VerifEtudiantRequest;
use App\Services\VerifService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class VerifApiController extends Controller
{
    protected $service;

    public function __construct(VerifService $service)
    {
        $this->service = $service;
    }

/*    public function verifEtudiant(VerifEtudiantRequest $request)
    {
        $data = $this->service->verifEtudiant(
            $request->input('id_etud'),
            $request->input('date_naissance')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404); // Code HTTP 404
        }


        return response()->json($data);
    }
    public function verifCitoyen(VerifCitoyenResquest $request)
    {
        
        $data = $this->service->verifCitoyen(
            $request->input('cin'),
            $request->input('jourNaiss'),
            $request->input('moisNaiss'),
            $request->input('anneeNaiss')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404);
        }
        return response()->json($data);
    }*/

public function verifEtudiant(VerifEtudiantRequest $request)
{
    $data = $this->service->verifEtudiant(
        $request->input('id_etud'),
        $request->input('date_naissance')
    );

    if (!$data || empty($data['inscriptions'])) {
        return response()->json([
            'status' => 'error',
            'message' => 'Student not found'
        ], 404);
    }

    $inscription = $data['inscriptions'][0];

    $responseData = [
        'data' => [
            'cnss_etab' => $inscription['cnss_etab'] ?? '',
            'cycle' => $inscription['cycle'] ?? '',
            'date_naissance' =>  $request->input('date_naissance') ?? '',
            'diplome' => $inscription['diplome'] ?? '',
            'email_o365' => $inscription['email_o365'] ?? '',
            'email_perso' => $inscription['email_perso'] ?? '',
            'etab' => $inscription['etab'] ?? '',
            'id_etud' => $request->input('id_etud') ?? '',
            'id_univ' => $inscription['id_univ'] ?? '',
            'niveau' => $inscription['niveau'] ?? '',
            'nom_insc' => $inscription['nom_insc'] ?? '',
            'prenom_insc' => $inscription['prenom_insc'] ?? '',
            'univ' => $inscription['univ'] ?? ''
        ],
        'status' => 'success'
    ];

    return response()->json($responseData, 200);
}

public function verifCitoyen(VerifCitoyenResquest $request)
{
 
    $data = $this->service->verifCitoyen(
        $request->input('cin'),
        $request->input('jourNaiss'),
        $request->input('moisNaiss'),
        $request->input('anneeNaiss')
    );

    if (!$data) {
        return response()->json([
            'status' => 'error',
            'message' => 'Citizen not found'
        ], 404);
    }

    return response()->json([
        'data' => [
            'anneeNaiss' => $request->input('anneeNaiss') ?? '',
            'cin' => $request->input('cin') ?? '',
            'codeR' => $data['codeR'] ?? '1',
            'jourNaiss' => $request->input('jourNaiss') ?? '',
            'moisNaiss' => $request->input('moisNaiss') ?? '',
            'nomAr' => $data['nomAr'] ?? '',
            'nomFr' => $data['nomFr'] ?? '',
            'prenomAr' => $data['prenomAr'] ?? '',
            'prenomFr' => $data['prenomFr'] ?? ''
        ],
        'status' => 'success'
    ], 200);
}

    public function verifCitoyenFamille(VerifCitoyenFamilleResquest $request)
    {
        $data = $this->service->verifCitoyenFamille(
            $request->input('idEdu'),
            $request->input('jourNaiss'),
            $request->input('moisNaiss'),
            $request->input('anneeNaiss')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404);
        }
        return response()->json($data);
    }

    // public function verifEleve(VerifEleveRequest $request)
    // {
    //     $data = $this->service->verifEleve(
    //         $request->input('identifiant'),
    //         $request->input('date_naissance')
    //     );
    //     if (!$data) {
    //         return response()->json([
    //             'error' => 'Item not found'
    //         ], 404); // Code HTTP 404
    //     }

    //     return response()->json($data);
    // }

    // public function verifCivile(VerifCivilRequest $request)
    // {
    //     $data = $this->service->verifCivile(
    //         $request->input('cin')
    //     );

    //     if (!$data) {
    //         return response()->json([
    //             'error' => 'Item not found'
    //         ], 404); // Code HTTP 404
    //     }

    //     return response()->json($data);
    // }

}
