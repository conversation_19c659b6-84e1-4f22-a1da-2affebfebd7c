<?php

namespace App\Repositories;

use App\Models\Station;
use Prettus\Repository\Eloquent\BaseRepository;

class StationRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'longitude' => '=',
        'latitude' => '=',
        'id_delegation' => '=',
        'id_governorate' => '='
    ];

    public function model(): string
    {
        return Station::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
