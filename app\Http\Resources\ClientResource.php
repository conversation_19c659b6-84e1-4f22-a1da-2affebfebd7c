<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'lastname' => $this->lastname,
            'firstname' => $this->firstname,
            'society_name' => $this->society_name,
            'legal_representative' => $this->legal_representative,
            'dob' => $this->dob,
            'phone' => $this->phone,
            'identity_number' => $this->identity_number,
            'address' => $this->address,
            'email' => $this->email,
            'is_moral' => $this->is_moral,
            'is_withTVA' => $this->is_withTVA,
            'delegation' => new DelegationResource($this->whenLoaded('delegation')),
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            'establishment' => new EstablishmentResource($this->whenLoaded('establishment')),
            'degree' => new DegreeResource($this->whenLoaded('degree')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
