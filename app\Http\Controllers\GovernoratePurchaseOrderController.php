<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreGovernoratePurchaseOrderRequest;
use App\Http\Requests\UpdateGovernoratePurchaseOrderRequest;
use App\Http\Resources\GovernoratePurchaseOrderResource;
use App\Models\Governorate;
use App\Models\GovernoratePurchaseOrder;
use App\Repositories\GovernoratePurchaseOrderRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GovernoratePurchaseOrderController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(GovernoratePurchaseOrderRepository::class);
        $this->authorizeResource(GovernoratePurchaseOrder::class, 'governoratePurchaseOrder');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $orders = $this->repository
                    ->with(['governorate'])
                    ->latest()
                    ->paginate();
        return GovernoratePurchaseOrderResource::collection($orders);
    }

    public function store(StoreGovernoratePurchaseOrderRequest $request): JsonResponse
    {
        $governorate = $request->input('id_governorate');

        $data = $request->validated();
        // Set current_amount equal to initial_amount when creating
        if (!isset($data['current_amount']) && isset($data['initial_amount'])) {
            $data['current_amount'] = $data['initial_amount'];
        }

        $order = $this->repository->create($data);
        $order->load(['governorate']);

        $this->updatePurchaseAmount($governorate);

        return response()->json([
            'message' => 'Governorate purchase order created successfully',
            'data' => new GovernoratePurchaseOrderResource($order)
        ], 201);
    }

    public function show(GovernoratePurchaseOrder $governoratePurchaseOrder): GovernoratePurchaseOrderResource
    {
        $governoratePurchaseOrder->load(['governorate']);
        return new GovernoratePurchaseOrderResource($governoratePurchaseOrder);
    }

    public function update(UpdateGovernoratePurchaseOrderRequest $request, GovernoratePurchaseOrder $governoratePurchaseOrder): JsonResponse
    {
        $governorate = $request->input('id_governorate');

        $order = $this->repository->update($request->validated(), $governoratePurchaseOrder->id);
        $order->load(['governorate']);

        $this->updatePurchaseAmount($governorate);

        return response()->json([
            'message' => 'Governorate purchase order updated successfully',
            'data' => new GovernoratePurchaseOrderResource($order)
        ]);
    }

    public function destroy(GovernoratePurchaseOrder $governoratePurchaseOrder): JsonResponse
    {
        $id_governorate = $governoratePurchaseOrder->id_governorate;

        $this->repository->delete($governoratePurchaseOrder->id);

        $this->updatePurchaseAmount($id_governorate);

        return response()->json([
            'message' => 'Governorate purchase order deleted successfully'
        ]);
    }

    public function getByGovernorate($governorateId): AnonymousResourceCollection
    {
        $orders = $this->repository->with(['governorate'])
            ->findWhere(['id_governorate' => $governorateId]);
        return GovernoratePurchaseOrderResource::collection($orders);
    }


    public function updatePurchaseAmount($governorateId): void
    {
        $governorate = Governorate::find($governorateId);
        $governorate->purchase_amount = $this->repository->findWhere([
            'id_governorate' => $governorateId,
            'status' => true
            ])->sum('current_amount');
        $governorate->save();
    }

}