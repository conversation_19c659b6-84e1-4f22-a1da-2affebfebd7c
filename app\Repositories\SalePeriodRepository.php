<?php

namespace App\Repositories;

use App\Models\SalePeriod;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use Illuminate\Support\Facades\DB;

class SalePeriodRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'date_start' => '>=',
        'date_end' => '<=',
        'id_abn_type' => '=',
        'id_campaign' => '=',
        'status' => '='
    ];

    public function model(): string
    {
        return SalePeriod::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $salePeriod = $this->find($id);

            if (!$salePeriod) {
                throw new \Exception('Sale period not found');
            }

            $salePeriod->affectationAgents()->delete();

            // Delete the sale period
            $result = parent::delete($id);

            DB::commit();
            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}

