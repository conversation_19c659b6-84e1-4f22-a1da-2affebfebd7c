<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubsTypeRequest;
use App\Http\Requests\UpdateSubsTypeRequest;
use App\Http\Resources\SubsTypeResource;
use App\Models\SubsType;
use App\Repositories\SubsTypeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SubsTypeController extends Controller
{
    private SubsTypeRepository $repository;

    public function __construct(SubsTypeRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SubsType::class, 'subs_type');
    }

    public function index(): AnonymousResourceCollection
    {
        return SubsTypeResource::collection($this->repository->latest()->paginate());
    }

    public function all(): AnonymousResourceCollection
    {
        return SubsTypeResource::collection($this->repository->all());
    }

    public function store(StoreSubsTypeRequest $request): JsonResponse
    {
        $subsType = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Subscription type created successfully',
            'data' => new SubsTypeResource($subsType)
        ], 201);
    }

    public function show(SubsType $subsType): SubsTypeResource
    {
        return new SubsTypeResource($subsType);
    }

    public function update(UpdateSubsTypeRequest $request, SubsType $subsType): JsonResponse
    {
        $subsType = $this->repository->update($request->validated(), $subsType->id);

        return response()->json([
            'message' => 'Subscription type updated successfully',
            'data' => new SubsTypeResource($subsType)
        ]);
    }

    public function destroy(SubsType $subsType): JsonResponse
    {
        $this->repository->delete($subsType->id);

        return response()->json([
            'message' => 'Subscription type deleted successfully'
        ]);
    }
}
