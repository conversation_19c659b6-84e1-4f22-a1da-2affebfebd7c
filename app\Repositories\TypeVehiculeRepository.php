<?php

namespace App\Repositories;

use App\Models\TypeVehicule;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TypeVehiculeRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'code' => 'like',
        'nbre_max_place' => '=',
        'status' => '='
    ];

    public function model(): string
    {
        return TypeVehicule::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
