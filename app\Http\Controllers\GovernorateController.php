<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreGovernorateRequest;
use App\Http\Requests\UpdateGovernorateRequest;
use App\Http\Resources\GovernorateResource;
use App\Models\Governorate;
use App\Repositories\GovernorateRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GovernorateController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(GovernorateRepository::class);
        $this->authorizeResource(Governorate::class, 'governorate');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $governorates = $this->repository
            ->with('governoratePurchaseOrders')
            ->latest()
            ->paginate($request->input('perPage'));
        return GovernorateResource::collection($governorates);
    }

    public function all(): AnonymousResourceCollection
    {
        $governorates = $this->repository->all();
        return GovernorateResource::collection($governorates);
    }

    public function store(StoreGovernorateRequest $request): JsonResponse
    {
        $governorate = $this->repository->create($request->validated());
        return response()->json([
            'message' => 'Governorate created successfully',
            'data' => new GovernorateResource($governorate)
        ], 201);
    }

    public function show(Governorate $governorate): GovernorateResource
    {
        return new GovernorateResource($governorate);
    }

    public function update(UpdateGovernorateRequest $request, Governorate $governorate): JsonResponse
    {
        $governorate = $this->repository->update($request->validated(), $governorate->id);
        return response()->json([
            'message' => 'Governorate updated successfully',
            'data' => new GovernorateResource($governorate)
        ]);
    }

    public function destroy(Governorate $governorate): JsonResponse
    {
        try {
            $this->repository->delete($governorate->id);

            return response()->json([
                'message' => 'Governorate deleted successfully'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Degree cannot be deleted, it is used in other records'
            ], 422);
        }
    }
}
