<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTripRequest;
use App\Http\Requests\UpdateTripRequest;
use App\Http\Resources\TripResource;
use App\Models\Trip;
use App\Repositories\TripRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TripController extends Controller
{
    private TripRepository $repository;

    public function __construct(TripRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Trip::class, 'trip');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return TripResource::collection(
            $this->repository
                ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
                ->latest()->where('inter_station', false)
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->all());
    }

    public function store(StoreTripRequest $request): JsonResponse
    {
        $data = $request->validated();

        // verifier si le trip existe déjà
        $existingTrip = Trip::where('id_line', $data['id_line'])
            ->where('id_station_start', $data['stations']['id_station_start'])
            ->where('id_station_end', $data['stations']['id_station_end'])
            ->where('inter_station', false)
            ->first();

        if ($existingTrip) {
            return response()->json([
                'message' => 'Trip_already_exists',
                'data' => new TripResource($existingTrip)
            ], 409);
        }

        $trip = $this->repository->create($data);

        return response()->json([
            'message' => 'Trip created successfully',
            'data' => new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']))
        ], 201);
    }

    public function show(Trip $trip): TripResource
    {
        return new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']));
    }

    public function update(UpdateTripRequest $request, Trip $trip): JsonResponse
    {
        try {
            $trip = $this->repository->update($request->validated(), $trip->id);

            return response()->json([
                'message' => 'Trip updated successfully',
                'data' => new TripResource($trip)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Trip $trip): JsonResponse
    {
        try {
            $this->repository->delete($trip->id);

            return response()->json([
                'message' => 'Trip deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_delete_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}




