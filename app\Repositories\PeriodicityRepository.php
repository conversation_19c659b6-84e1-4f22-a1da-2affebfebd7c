<?php

namespace App\Repositories;

use App\Models\Periodicity;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class PeriodicityRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'periodicity_code' => 'like',
        'max_days_per_week' => '='
    ];

    public function model(): string
    {
        return Periodicity::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
