<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreEstablishmentRequest;
use App\Http\Requests\UpdateEstablishmentRequest;
use App\Http\Resources\EstablishmentResource;
use App\Models\Establishment;
use App\Repositories\EstablishmentRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class EstablishmentController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(EstablishmentRepository::class);
        $this->authorizeResource(Establishment::class, 'establishment');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $establishments = $this->repository
            ->with(['delegation.governorate', 'typeEstablishment'])
            ->latest()
            ->paginate();
        return EstablishmentResource::collection($establishments);
    }

    public function all(): AnonymousResourceCollection
    {
        $establishments = $this->repository->with(['delegation', 'typeEstablishment'])->all();
        return EstablishmentResource::collection($establishments);
    }

    public function store(StoreEstablishmentRequest $request): JsonResponse
    {
        $establishment = $this->repository->create($request->validated());
        $establishment->load(['delegation', 'typeEstablishment']);

        return response()->json([
            'message' => 'Establishment created successfully',
            'data' => new EstablishmentResource($establishment)
        ], 201);
    }

    public function show(Establishment $establishment): EstablishmentResource
    {
        $establishment->load(['delegation', 'typeEstablishment']);
        return new EstablishmentResource($establishment);
    }

    public function update(UpdateEstablishmentRequest $request, Establishment $establishment): JsonResponse
    {
        $establishment = $this->repository->update($request->validated(), $establishment->id);
        $establishment->load(['delegation', 'typeEstablishment']);

        return response()->json([
            'message' => 'Establishment updated successfully',
            'data' => new EstablishmentResource($establishment)
        ]);
    }

    public function destroy(Establishment $establishment): JsonResponse
    {
        try {
            $this->repository->delete($establishment->id);
            return response()->json([
                'message' => 'Establishment deleted successfully'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Establishment cannot be deleted, it is used in other records'
            ], 422);
        }
    }

    public function getByDelegation($delegationId): AnonymousResourceCollection
    {
        $establishments = $this->repository->with(['delegation', 'typeEstablishment'])
            ->findWhere(['id_delegation' => $delegationId]);
        return EstablishmentResource::collection($establishments);
    }

    public function getByType($typeId): AnonymousResourceCollection
    {
        $establishments = $this->repository->with(['delegation', 'typeEstablishment'])
            ->findWhere(['id_type_establishment' => $typeId]);
        return EstablishmentResource::collection($establishments);
    }
}
