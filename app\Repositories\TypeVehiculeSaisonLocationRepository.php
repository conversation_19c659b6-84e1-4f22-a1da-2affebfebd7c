<?php

namespace App\Repositories;

use App\Models\TypeVehiculeSaisonLocation;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TypeVehiculeSaisonLocationRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_type_vehicule' => '=',
        'id_saison_location' => '=',
        'prix_km' => '=',
        'status' => '='
    ];

    public function model(): string
    {
        return TypeVehiculeSaisonLocation::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
