<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TypeVehiculeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'code' => $this->code,
            'nbre_max_place' => $this->nbre_max_place,
            'swf' => $this->swf,
            'photo' => $this->photo,
            'status' => $this->status,
            'type_vehicle_type_locations' => TypeVehicleTypeLocationResource::collection($this->whenLoaded('typeVehicleTypeLocations')),
            'type_vehicule_saison_locations' => TypeVehiculeSaisonLocationResource::collection($this->whenLoaded('typeVehiculeSaisonLocations')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
