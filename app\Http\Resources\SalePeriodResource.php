<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalePeriodResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'date_start' => $this->date_start,
            'date_end' => $this->date_end,
            'id_campaign' => $this->id_campaign,
            'id_abn_type' => $this->id_abn_type,
            'status' => $this->status,
            'campaign' => new CampaignResource($this->whenLoaded('campaign')),
            'abn_type' => new SubsTypeResource($this->whenLoaded('abnType')),
            //'affectation_agents' => AffectationAgentResource::collection($this->whenLoaded('affectationAgents')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

