<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAcademicYearRequest;
use App\Http\Requests\UpdateAcademicYearRequest;
use App\Http\Resources\AcademicYearResource;
use App\Models\AcademicYear;
use App\Repositories\AcademicYearRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class AcademicYearController extends Controller
{
    private AcademicYearRepository $repository;

    public function __construct(AcademicYearRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(AcademicYear::class, 'academic_year');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        return AcademicYearResource::collection(
            $this->repository
                ->orderBy($request->orderBy ?? 'code', $request->sortedBy ?? 'desc')
                ->paginate($request->input('perPage'))
        );
    }

    /**
     * Get all academic years without pagination.
     */
    public function all(): AnonymousResourceCollection
    {
        return AcademicYearResource::collection(
            $this->repository->orderBy('code', 'desc')->all()
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAcademicYearRequest $request): JsonResponse
    {
        $academicYear = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Academic year created successfully',
            'data' => new AcademicYearResource($academicYear)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(AcademicYear $academicYear): AcademicYearResource
    {
        return new AcademicYearResource($academicYear);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAcademicYearRequest $request, AcademicYear $academicYear): JsonResponse
    {
        $academicYear = $this->repository->update($request->validated(), $academicYear->id);

        return response()->json([
            'message' => 'Academic year updated successfully',
            'data' => new AcademicYearResource($academicYear)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AcademicYear $academicYear): JsonResponse
    {
        $this->repository->delete($academicYear->id);

        return response()->json([
            'message' => 'Academic year deleted successfully'
        ]);
    }
}
