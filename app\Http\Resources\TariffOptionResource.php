<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TariffOptionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_trip' => $this->id_trip,
            'id_subs_type' => $this->id_subs_type,
            'is_regular' => $this->is_regular,
            'id_tariff_base' => $this->when($this->is_regular, $this->id_tariff_base),
            'manual_tariff' => $this->when(!$this->is_regular, $this->manual_tariff),
            'trip' => new TripResource($this->whenLoaded('trip')),
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'tariff_base' => new TariffBaseResource($this->whenLoaded('tariffBase')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}