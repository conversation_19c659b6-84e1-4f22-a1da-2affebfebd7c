<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLocationTypeRequest;
use App\Http\Requests\UpdateLocationTypeRequest;
use App\Http\Resources\LocationTypeResource;
use App\Models\LocationType;
use App\Repositories\LocationTypeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class LocationTypeController extends Controller
{
    private LocationTypeRepository $repository;

    public function __construct(LocationTypeRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(LocationType::class, 'location_type');
    }

    public function index(): AnonymousResourceCollection
    {
        return LocationTypeResource::collection(
            $this->repository
                ->latest()
                ->paginate()
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return LocationTypeResource::collection($this->repository->all());
    }

    public function store(StoreLocationTypeRequest $request): LocationTypeResource
    {
        $locationType = $this->repository->create($request->validated());
        return new LocationTypeResource($locationType);
    }

    public function show(LocationType $locationType): LocationTypeResource
    {
        return new LocationTypeResource($locationType);
    }

    public function update(UpdateLocationTypeRequest $request, LocationType $locationType): LocationTypeResource
    {
        $locationType = $this->repository->update($request->validated(), $locationType->id);
        return new LocationTypeResource($locationType);
    }

    public function destroy(LocationType $locationType): JsonResponse
    {
        try {
            $this->repository->delete($locationType->id);
            return response()->json(null, 204);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Location type cannot be deleted, it is used in other records'
            ], 422);
        }
    }
}
