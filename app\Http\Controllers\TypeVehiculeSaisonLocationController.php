<?php


namespace App\Http\Controllers;

use App\Http\Requests\StoreTypeVehiculeSaisonLocationRequest;
use App\Http\Requests\UpdateTypeVehiculeSaisonLocationRequest;
use App\Http\Resources\TypeVehiculeSaisonLocationResource;
use App\Models\TypeVehiculeSaisonLocation;
use App\Repositories\TypeVehiculeSaisonLocationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TypeVehiculeSaisonLocationController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(TypeVehiculeSaisonLocationRepository::class);
        $this->authorizeResource(TypeVehiculeSaisonLocation::class, 'type_vehicule_saison_location');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $typeVehiculeSaisonLocations = $this->repository->latest()->with(['typeVehicule', 'season'])->paginate();
        return TypeVehiculeSaisonLocationResource::collection($typeVehiculeSaisonLocations);
    }

    public function all(): AnonymousResourceCollection
    {
        $typeVehiculeSaisonLocations = $this->repository->with(['typeVehicule', 'season'])->all();
        return TypeVehiculeSaisonLocationResource::collection($typeVehiculeSaisonLocations);
    }

    public function store(StoreTypeVehiculeSaisonLocationRequest $request): JsonResponse
    {
        $typeVehiculeSaisonLocation = $this->repository->create($request->validated());
        $typeVehiculeSaisonLocation->load(['typeVehicule', 'season']);

        return response()->json([
            'message' => 'Type vehicule saison location created successfully',
            'data' => new TypeVehiculeSaisonLocationResource($typeVehiculeSaisonLocation)
        ], 201);
    }

    public function show(TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): TypeVehiculeSaisonLocationResource
    {
        $typeVehiculeSaisonLocation->load(['typeVehicule', 'season']);
        return new TypeVehiculeSaisonLocationResource($typeVehiculeSaisonLocation);
    }

    public function update(UpdateTypeVehiculeSaisonLocationRequest $request, TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): JsonResponse
    {
        $typeVehiculeSaisonLocation = $this->repository->update($request->validated(), $typeVehiculeSaisonLocation->id);
        $typeVehiculeSaisonLocation->load(['typeVehicule', 'season']);

        return response()->json([
            'message' => 'Type vehicule saison location updated successfully',
            'data' => new TypeVehiculeSaisonLocationResource($typeVehiculeSaisonLocation)
        ]);
    }

    public function destroy(TypeVehiculeSaisonLocation $typeVehiculeSaisonLocation): JsonResponse
    {
        $this->repository->delete($typeVehiculeSaisonLocation->id);
        return response()->json([
            'message' => 'Type vehicule saison location deleted successfully'
        ]);
    }

    public function getByTypeVehicule($typeVehiculeId): AnonymousResourceCollection
    {
        $typeVehiculeSaisonLocations = $this->repository->with(['typeVehicule', 'season'])
            ->findWhere(['id_type_vehicule' => $typeVehiculeId]);
        return TypeVehiculeSaisonLocationResource::collection($typeVehiculeSaisonLocations);
    }

    public function getBySeason($seasonId): AnonymousResourceCollection
    {
        $typeVehiculeSaisonLocations = $this->repository->with(['typeVehicule', 'season'])
            ->findWhere(['id_saison_location' => $seasonId]);
        return TypeVehiculeSaisonLocationResource::collection($typeVehiculeSaisonLocations);
    }
}
