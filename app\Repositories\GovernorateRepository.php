<?php

namespace App\Repositories;

use App\Models\Governorate;
use Prettus\Repository\Eloquent\BaseRepository;

class GovernorateRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Governorate::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'code' => '=',
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'purchase_amount' => 'between'
    ];
}

