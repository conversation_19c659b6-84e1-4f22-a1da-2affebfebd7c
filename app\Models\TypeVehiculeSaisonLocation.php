<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TypeVehiculeSaisonLocation extends Model
{
    protected $fillable = [
        'id_type_vehicule',
        'id_saison_location',
        'prix_km',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
        'prix_km' => 'decimal:2'
    ];

    /**
     * The attributes that should be unique.
     *
     * @var array
     */
    protected $uniqueKeys = [
        ['id_type_vehicule', 'id_saison_location']
    ];

    public function typeVehicule(): BelongsTo
    {
        return $this->belongsTo(TypeVehicule::class, 'id_type_vehicule');
    }

    public function season(): BelongsTo
    {
        return $this->belongsTo(LocationSeason::class, 'id_saison_location');
    }
}

