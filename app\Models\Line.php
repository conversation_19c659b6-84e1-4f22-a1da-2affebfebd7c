<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Line extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'CODE_LINE',
        'type_service',
        'status',
        'commercial_speed'
    ];

    protected $casts = [
        'status' => 'boolean',
        'commercial_speed' => 'integer'
    ];

    public function trips(): HasMany
    {
        return $this->hasMany(Trip::class, 'id_line');
    }

    public function lineStations(): HasMany
    {
        return $this->hasMany(LineStation::class, 'id_line');
    }

    public function websiteTrips(): Has<PERSON>any
    {
        return $this->hasMany(WebsiteTrip::class, 'id_line');
    }
}

