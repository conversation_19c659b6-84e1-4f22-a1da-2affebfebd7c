<?php


namespace App\Http\Controllers;

use App\Http\Requests\StoreTypeVehicleTypeLocationRequest;
use App\Http\Requests\UpdateTypeVehicleTypeLocationRequest;
use App\Http\Resources\TypeVehicleTypeLocationResource;
use App\Models\TypeVehicleTypeLocation;
use App\Repositories\TypeVehicleTypeLocationRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TypeVehicleTypeLocationController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(TypeVehicleTypeLocationRepository::class);
        $this->authorizeResource(TypeVehicleTypeLocation::class, 'type_vehicle_type_location');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $typeVehicleTypeLocations = $this->repository->latest()->with(['typeVehicule', 'locationType'])->paginate();
        return TypeVehicleTypeLocationResource::collection($typeVehicleTypeLocations);
    }

    public function all(): AnonymousResourceCollection
    {
        $typeVehicleTypeLocations = $this->repository->with(['typeVehicule', 'locationType'])->all();
        return TypeVehicleTypeLocationResource::collection($typeVehicleTypeLocations);
    }

    public function store(StoreTypeVehicleTypeLocationRequest $request): JsonResponse
    {
        $typeVehicleTypeLocation = $this->repository->create($request->validated());
        $typeVehicleTypeLocation->load(['typeVehicule', 'locationType']);

        return response()->json([
            'message' => 'Type vehicle type location created successfully',
            'data' => new TypeVehicleTypeLocationResource($typeVehicleTypeLocation)
        ], 201);
    }

    public function show(TypeVehicleTypeLocation $typeVehicleTypeLocation): TypeVehicleTypeLocationResource
    {
        $typeVehicleTypeLocation->load(['typeVehicule', 'locationType']);
        return new TypeVehicleTypeLocationResource($typeVehicleTypeLocation);
    }

    public function update(UpdateTypeVehicleTypeLocationRequest $request, TypeVehicleTypeLocation $typeVehicleTypeLocation): JsonResponse
    {
        $typeVehicleTypeLocation = $this->repository->update($request->validated(), $typeVehicleTypeLocation->id);
        $typeVehicleTypeLocation->load(['typeVehicule', 'locationType']);

        return response()->json([
            'message' => 'Type vehicle type location updated successfully',
            'data' => new TypeVehicleTypeLocationResource($typeVehicleTypeLocation)
        ]);
    }

    public function destroy(TypeVehicleTypeLocation $typeVehicleTypeLocation): JsonResponse
    {
        $this->repository->delete($typeVehicleTypeLocation->id);
        return response()->json([
            'message' => 'Type vehicle type location deleted successfully'
        ]);
    }

    public function getByTypeVehicule($typeVehiculeId): AnonymousResourceCollection
    {
        $typeVehicleTypeLocations = $this->repository->with(['typeVehicule', 'locationType'])
            ->findWhere(['id_type_vehicule' => $typeVehiculeId]);
        return TypeVehicleTypeLocationResource::collection($typeVehicleTypeLocations);
    }

    public function getByLocationType($locationTypeId): AnonymousResourceCollection
    {
        $typeVehicleTypeLocations = $this->repository->with(['typeVehicule', 'locationType'])
            ->findWhere(['id_type_location' => $locationTypeId]);
        return TypeVehicleTypeLocationResource::collection($typeVehicleTypeLocations);
    }
}
