<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCardTypeRequest;
use App\Http\Requests\UpdateCardTypeRequest;
use App\Http\Resources\CardTypeResource;
use App\Models\CardType;
use App\Repositories\CardTypeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CardTypeController extends Controller
{
    private CardTypeRepository $repository;

    public function __construct(CardTypeRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(CardType::class, 'card_type');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return CardTypeResource::collection(
            $this->repository
                ->latest()
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return CardTypeResource::collection($this->repository->all());
    }

    public function store(StoreCardTypeRequest $request): JsonResponse
    {
        $cardType = $this->repository->create($request->validated());
        
        return response()->json([
            'message' => 'Card type created successfully',
            'data' => new CardTypeResource($cardType)
        ], 201);
    }

    public function show(CardType $cardType): CardTypeResource
    {
        return new CardTypeResource($cardType);
    }

    public function update(UpdateCardTypeRequest $request, CardType $cardType): JsonResponse
    {
        $cardType = $this->repository->update($request->validated(), $cardType->id);
        
        return response()->json([
            'message' => 'Card type updated successfully',
            'data' => new CardTypeResource($cardType)
        ]);
    }

    public function destroy(CardType $cardType): JsonResponse
    {
        if ($cardType->affectationCardTypes()->exists() || $cardType->subsCards()->exists()) {
            return response()->json([
                'message' => trans('messages.card_type.delete.error'),
                'error' => 'Resource in use'
            ], 422);
        }

        $this->repository->delete($cardType->id);
        
        return response()->json([
            'message' => trans('messages.card_type.delete.success')
        ]);
    }
}


