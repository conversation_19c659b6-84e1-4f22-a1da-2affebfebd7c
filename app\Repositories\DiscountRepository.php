<?php

namespace App\Repositories;

use App\Models\Discount;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class DiscountRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'is_stagiaire' => '=',
        'special_client' => '=',
        'date_start' => 'between',
        'date_end' => 'between',
        'percentage' => '=',
        'id_subs_type' => '='
    ];

    public function model(): string
    {
        return Discount::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}

