<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreClientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'lastname' => 'required|string|max:255',
            'firstname' => 'required|string|max:255',
            'dob' => 'required|date',
            'phone' => 'required|numeric|unique:clients,phone',
            'identity_number' => 'required|string|unique:clients,identity_number',
            'address' => 'required|string',
            'email' => 'required|email|unique:clients,email',
            'password' => 'required|string|min:6',
            'id_client_type' => 'required|exists:type_clients,id',
            'id_delegation' => 'required|exists:delegations,id',
            'id_governorate' => 'required|exists:governorates,id',
            'id_establishment' => 'nullable|exists:establishments,id',
            'id_degree' => 'nullable|exists:degrees,id'
        ];
    }
}
