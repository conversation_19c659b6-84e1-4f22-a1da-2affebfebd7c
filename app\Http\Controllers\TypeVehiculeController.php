<?php


namespace App\Http\Controllers;

use App\Http\Requests\StoreTypeVehiculeRequest;
use App\Http\Requests\UpdateTypeVehiculeRequest;
use App\Http\Resources\TypeVehiculeResource;
use App\Models\TypeVehicule;
use App\Repositories\TypeVehiculeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TypeVehiculeController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(TypeVehiculeRepository::class);
        $this->authorizeResource(TypeVehicule::class, 'type_vehicule');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $typeVehicules = $this->repository->latest()->paginate();
        return TypeVehiculeResource::collection($typeVehicules);
    }

    public function all(): AnonymousResourceCollection
    {
        $typeVehicules = $this->repository->where('status', 1)->get();
        return TypeVehiculeResource::collection($typeVehicules);
    }

    public function store(StoreTypeVehiculeRequest $request): JsonResponse
    {
        $typeVehicule = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Type vehicule created successfully',
            'data' => new TypeVehiculeResource($typeVehicule)
        ], 201);
    }

    public function show(TypeVehicule $typeVehicule): TypeVehiculeResource
    {
        $typeVehicule->load(['typeVehicleTypeLocations.locationType', 'typeVehiculeSaisonLocations.season']);
        return new TypeVehiculeResource($typeVehicule);
    }

    public function update(UpdateTypeVehiculeRequest $request, TypeVehicule $typeVehicule): JsonResponse
    {
        $typeVehicule = $this->repository->update($request->validated(), $typeVehicule->id);

        return response()->json([
            'message' => 'Type vehicule updated successfully',
            'data' => new TypeVehiculeResource($typeVehicule)
        ]);
    }

    public function destroy(TypeVehicule $typeVehicule): JsonResponse
    {
        try {
        $this->repository->delete($typeVehicule->id);
        return response()->json([
            'message' => 'Type vehicule deleted successfully'
        ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Type vehicule cannot be deleted, it is used in other records',
                'error' => $e->getMessage()
            ], 422);
        }
    }
}
