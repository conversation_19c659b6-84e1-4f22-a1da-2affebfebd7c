<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSubsDuplicationRequest;
use App\Http\Resources\SubsDuplicationResource;
use App\Models\SubsDuplication;
use App\Repositories\SubsDuplicationRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SubsDuplicationController extends Controller
{
    private SubsDuplicationRepository $repository;

    public function __construct(SubsDuplicationRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SubsDuplication::class, 'duplicata');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //
        return SubsDuplicationResource::collection(
            $this->repository
                        ->latest()
                        ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return SubsDuplication::collection($this->repository->all());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubsDuplicationRequest $request)
    {
        //
        //$request->admin_id = $request->user()->id ;
        $data = $request->validated() ;
        $data["admin_id"] = $request->user()->id;

       // dd($data);
        
        $sd = $this->repository->create( $data);

        return response()->json([
            'message' => 'SubsDuplication created successfully',
            'data' => new SubsDuplicationResource($sd)
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
