<?php

namespace App\Repositories;

use App\Models\Season;
use Prettus\Repository\Eloquent\BaseRepository;

class SeasonRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'start_date' => '>=',
        'end_date' => '<=',
        'priority' => '='
    ];

    public function model(): string
    {
        return Season::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
