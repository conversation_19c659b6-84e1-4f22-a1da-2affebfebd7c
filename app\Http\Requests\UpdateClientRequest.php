<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateClientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'lastname' => 'required|string|max:255',
            'firstname' => 'required|string|max:255',
            'dob' => 'required|date',
            'phone' => 'required|numeric',
            'identity_number' => 'required|string',
            'address' => 'required|string',
            'email' => 'nullable|email',
            'password' => 'nullable|sometimes|string',
            'id_client_type' => 'required|exists:type_clients,id',
            'id_delegation' => 'required|exists:delegations,id',
            'id_governorate' => 'required|exists:governorates,id',
            'id_establishment' => 'nullable|exists:establishments,id',
            'id_degree' => 'nullable|exists:degrees,id'
        ];
    }
}