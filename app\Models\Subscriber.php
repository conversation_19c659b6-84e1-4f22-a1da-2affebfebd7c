<?php

namespace App\Models;

use <PERSON>mon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Subscriber extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable;

    protected $table = 'subscribers';

    protected $fillable = [
        'lastname',
        'firstname',
        'phone',
        'cin',
        'address',
        'email',
        'password'
    ];

    protected $hidden = [
        'password',
    ];

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }
}
