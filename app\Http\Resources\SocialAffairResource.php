<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SocialAffairResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'governorate_id' => $this->governorate_id,
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            'academic_year_id' => $this->academic_year_id,
            'academic_year' => new AcademicYearResource($this->whenLoaded('academicYear')),
            'delegation' => $this->delegation,
            'eleve_etudiant' => $this->eleve_etudiant,
            'societe' => $this->societe,
            'nom_parent' => $this->nom_parent,
            'cin_parent' => $this->cin_parent,
            'identifier' => $this->identifier,
            'dob' => $this->dob,
            'telephone' => $this->telephone,
            'nom_complet' => $this->nom_complet,
            'niveau_etude' => $this->niveau_etude,
            'trajet_requise' => $this->trajet_requise,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
