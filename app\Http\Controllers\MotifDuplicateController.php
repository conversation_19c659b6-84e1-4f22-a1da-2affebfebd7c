<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMotifDuplicateRequest;
use App\Http\Requests\UpdateMotifDuplicateRequest;
use App\Http\Resources\MotifDuplicateResource;
use App\Models\MotifDuplicate;
use App\Repositories\MotifDuplicateRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class MotifDuplicateController extends Controller
{
    private MotifDuplicateRepository $repository;

    public function __construct(MotifDuplicateRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(MotifDuplicate::class, 'motif_duplicate');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return MotifDuplicateResource::collection(
            $this->repository
                ->with(['cardType'])
                ->latest()
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return MotifDuplicateResource::collection(
            $this->repository->all()
        );
    }

    public function store(StoreMotifDuplicateRequest $request): JsonResponse
    {
        $motifDuplicate = $this->repository->create($request->validated());
        
        return response()->json([
            'message' => 'Motif duplicate created successfully',
            'data' => new MotifDuplicateResource($motifDuplicate)
        ], 201);
    }

    public function show(MotifDuplicate $motifDuplicate): MotifDuplicateResource
    {
        return new MotifDuplicateResource($motifDuplicate);
    }

    public function update(UpdateMotifDuplicateRequest $request, MotifDuplicate $motifDuplicate): JsonResponse
    {
        $motifDuplicate = $this->repository->update($request->validated(), $motifDuplicate->id);
        
        return response()->json([
            'message' => 'Motif duplicate updated successfully',
            'data' => new MotifDuplicateResource($motifDuplicate)
        ]);
    }

    public function destroy(MotifDuplicate $motifDuplicate): JsonResponse
    {
        $this->repository->delete($motifDuplicate->id);
        
        return response()->json([
            'message' => 'Motif duplicate deleted successfully'
        ]);
    }
}



