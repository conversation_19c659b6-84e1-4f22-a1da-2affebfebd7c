<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SalePointResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'contact' => $this->contact,
            'address' => $this->address,
            'status' => $this->status,
            'delegation' => new DelegationResource($this->whenLoaded('delegation')),
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            'agency' => new AgencyResource($this->whenLoaded('agency')),
            'subs_cards_count' => $this->whenCounted('subsCards'),
            'affectation_agents_count' => $this->whenCounted('affectationAgents'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}