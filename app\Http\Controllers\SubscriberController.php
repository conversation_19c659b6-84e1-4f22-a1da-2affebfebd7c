<?php

namespace App\Http\Controllers;

use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\ResetPasswordStepOneRequest;
use App\Http\Requests\ResetPasswordStepTwoRequest;
use App\Http\Requests\StoreSubscriberRequest;
use App\Http\Requests\StoreSubscriberStepFourRequest;
use App\Http\Requests\StoreSubscriberStepOneRequest;
use App\Http\Requests\StoreSubscriberStepThreeRequest;
use App\Http\Requests\StoreSubscriberStepTwoRequest;
use App\Http\Requests\UpdateEmailRequest;
use App\Http\Requests\UpdateEmailStepOneRequest;
use App\Http\Requests\UpdateEmailStepThreeRequest;
use App\Http\Requests\UpdateEmailStepTwoRequest;
use App\Http\Requests\UpdatePasswordRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Resources\SubscriberResource;
use App\Repositories\SubscriberRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;

class SubscriberController extends Controller
{
    protected SubscriberRepository $subscriberRepository;

    public function __construct(SubscriberRepository $subscriberRepository)
    {
        $this->subscriberRepository = $subscriberRepository;
    }

    public function store(StoreSubscriberRequest $request) : JsonResponse
    {
        $result = $this->subscriberRepository->verifyCode($request->email, $request->verificationCode);

        if (!$result) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        $subscriber = $this->subscriberRepository->create($request->validated());
        return response()->json([
            'message' => 'Subscriber created successfully',
            'data' => new SubscriberResource($subscriber)
        ], 201);
    }

    public function storeStepOne(StoreSubscriberStepOneRequest $request) : JsonResponse
    {
        return response()->json([
            'message' => 'Step one passed successfully',
        ], 200);
    }

    public function storeStepTwo(StoreSubscriberStepTwoRequest $request) : JsonResponse
    {
        return response()->json([
            'message' => 'Step two passed successfully',
        ], 200);
    }

    public function storeStepThree(StoreSubscriberStepThreeRequest $request) : JsonResponse
    {
        $this->subscriberRepository->sendVerificationEmail($request->validated(), 'Email verification');

        return response()->json([
            'message' => 'Step three passed successfully',
        ], 200);
    }

    public function storeStepFour(StoreSubscriberStepFourRequest $request) : JsonResponse
    {
        $result = $this->subscriberRepository->verifyCode($request->email, $request->verificationCode);

        if (!$result) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        return response()->json([
            'message' => 'Step four passed successfully',
        ], 200);
    }

    public function resetPassword(ResetPasswordRequest $request) : JsonResponse
    {
        $result = $this->subscriberRepository->verifyCode($request->email, $request->verificationCode);

        if (!$result) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        $this->subscriberRepository->resetPassword($request->validated());

        return response()->json([
            'message' => 'Password reset successfully',
        ], 201);
    }

    public function resetPasswordStepOne(ResetPasswordStepOneRequest $request) : JsonResponse
    {
        $this->subscriberRepository->sendVerificationEmail($request->validated(), 'Password reset');

        return response()->json([
            'message' => 'Step one passed successfully',
        ], 200);
    }

    public function resetPasswordStepTwo(ResetPasswordStepTwoRequest $request) : JsonResponse
    {
        $result = $this->subscriberRepository->verifyCode($request->email, $request->verificationCode);

        if (!$result) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        return response()->json([
            'message' => 'Step two passed successfully',
        ], 200);
    }

    public function updateProfile(UpdateProfileRequest $request) : JsonResponse
    {
        $this->subscriberRepository->updateProfile($request->validated());
        return response()->json([
            'message' => 'Profile updated successfully',
            'new_token' => auth()->refresh(),
        ], 200);
    }

    public function UpdatePassword(UpdatePasswordRequest $request) : JsonResponse
    {
        $subscriber = auth()->user();
        if (! Hash::check($request->oldPassword, $subscriber->password)) {
            return response()->json([
                'message' => 'Old password is not correct',
                'errors' => [
                    'oldPassword' => ['Old password is not correct']
                ]
            ], 400);
        }
        $this->subscriberRepository->UpdatePassword($request->validated());
        return response()->json([
            'message' => 'Password updated successfully'
        ], 200);
    }

    public function updateEmailStepOne(UpdateEmailStepOneRequest $request) : JsonResponse
    {
        $subscriber = auth()->user();
        if($subscriber->email !== $request->oldEmail) {
            return response()->json([
                'message' => 'Email is not correct',
                'errors' => [
                    'oldEmail' => ['Email is not correct',]
                ]
            ], 400);
        }

        $data = ["email"=> $request->oldEmail];
        $this->subscriberRepository->sendVerificationEmail($data, 'Email verification');

        return response()->json([
            'message' => 'Step one passed successfully',
        ], 200);
    }

    public function updateEmailStepTwo(UpdateEmailStepTwoRequest $request) : JsonResponse
    {
        $result = $this->subscriberRepository->verifyCode($request->oldEmail, $request->oldEmailVerificationCode);

        if (!$result) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        return response()->json([
            'message' => 'Step two passed successfully',
        ], 200);
    }

    public function updateEmailStepThree(UpdateEmailStepThreeRequest $request) : JsonResponse
    {
        $data = ["email"=> $request->newEmail];
        $this->subscriberRepository->sendVerificationEmail($data, 'Email verification');

        return response()->json([
            'message' => 'Step three passed successfully',
        ], 200);
    }

    public function updateEmail(UpdateEmailRequest $request) : JsonResponse
    {
        $subscriber = auth()->user();
        if($subscriber->email !== $request->oldEmail) {
            return response()->json([
                'message' => 'Old Email is not correct',
                'errors' => [
                    'oldEmail' => ['Old Email is not correct',]
                ]
            ], 400);
        }

        $resultOld = $this->subscriberRepository->verifyCode($request->oldEmail, $request->oldEmailVerificationCode);
        $resultNew = $this->subscriberRepository->verifyCode($request->newEmail, $request->newEmailVerificationCode);

        if (!$resultOld || !$resultNew) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 400);
        }

        $this->subscriberRepository->UpdateEmail($request->validated());

        return response()->json([
            'message' => 'Email updated successfully',
        ], 200);
    }

}
