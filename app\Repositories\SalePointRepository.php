<?php

namespace App\Repositories;

use App\Models\SalePoint;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class SalePointRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'contact' => 'like',
        'address' => 'like',
        'status' => '=',
        'id_delegation' => '=',
        'id_governorate' => '=',
        'id_agency' => '='
    ];

    public function model(): string
    {
        return SalePoint::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
