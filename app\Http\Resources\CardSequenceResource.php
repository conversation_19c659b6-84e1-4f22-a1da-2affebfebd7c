<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CardSequenceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_card_type' => $this->id_card_type,
            'start_sequence' => $this->start_sequence,
            'end_sequence' => $this->end_sequence,
            'status' => $this->status,
            'id_agent' => $this->id_agent,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'cardType' => new CardTypeResource($this->whenLoaded('cardType')),
            'agent' => new AdminResource($this->whenLoaded('agent')),
        ];
    }
}
