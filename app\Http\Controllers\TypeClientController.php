<?php

namespace App\Http\Controllers;

use App\Models\TypeClient;
use App\Repositories\TypeClientRepository;
use App\Http\Requests\StoreTypeClientRequest;
use App\Http\Requests\UpdateTypeClientRequest;
use App\Http\Resources\TypeClientResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TypeClientController extends Controller
{
    private TypeClientRepository $repository;

    public function __construct(TypeClientRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(TypeClient::class, 'type_client');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return TypeClientResource::collection(
            $this->repository->withCount('clients')->latest()->paginate($request->input('perPage'))
        );
    }


    public function all(): AnonymousResourceCollection
    {
        return TypeClientResource::collection(
            $this->repository->all()
        );
    }

    public function store(StoreTypeClientRequest $request): JsonResponse
    {
        $typeClient = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Client type created successfully',
            'data' => new TypeClientResource($typeClient)
        ], 201);
    }

    public function show(TypeClient $typeClient): TypeClientResource
    {
        return new TypeClientResource(
            $typeClient->loadCount('clients')
        );
    }

    public function update(UpdateTypeClientRequest $request, TypeClient $typeClient): JsonResponse
    {
        $typeClient = $this->repository->update($request->validated(), $typeClient->id);

        return response()->json([
            'message' => 'Client type updated successfully',
            'data' => new TypeClientResource($typeClient)
        ]);
    }

    public function destroy(TypeClient $typeClient): JsonResponse
    {
        if ($typeClient->clients()->exists()) {
            return response()->json([
                'message' => 'Cannot delete client type with associated clients',
                'error' => 'Resource in use'
            ], 422);
        }

        $this->repository->delete($typeClient->id);

        return response()->json([
            'message' => 'Client type deleted successfully'
        ]);
    }
}
