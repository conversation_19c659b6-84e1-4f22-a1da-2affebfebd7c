<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Trip extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_line',
        'id_station_start',
        'id_station_end',
        'status',
        'inter_station',
        'number_of_km'
    ];

    protected $casts = [
        'status' => 'boolean',
        'inter_station' => 'boolean',
        'number_of_km' => 'integer'
    ];

    public function line(): BelongsTo
    {
        return $this->belongsTo(Line::class, 'id_line');
    }

    public function startStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_start');
    }

    public function endStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_end');
    }

    public function tariffOptions(): HasMany
    {
        return $this->hasMany(TariffOption::class, 'id_trip');
    }
}



