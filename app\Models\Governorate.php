<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Governorate extends Model
{
    protected $fillable = [
        'code',
        'nom_fr',
        'nom_en',
        'nom_ar',
        'purchase_amount'
    ];

    protected $casts = [
        'purchase_amount' => 'decimal:2'
    ];

    public function delegations(): HasMany
    {
        return $this->hasMany(Delegation::class, 'id_governorate');
    }

    public function agencies(): HasMany
    {
        return $this->hasMany(Agency::class, 'id_governorate');
    }

    public function governoratePurchaseOrders(): HasMany
    {
        return $this->hasMany(GovernoratePurchaseOrder::class, 'id_governorate');
    }
}


