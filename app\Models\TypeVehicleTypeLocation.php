<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TypeVehicleTypeLocation extends Model
{
    protected $fillable = [
        'id_type_vehicule',
        'id_type_location',
        'km_min',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
        'km_min' => 'integer'
    ];

    public function typeVehicule(): BelongsTo
    {
        return $this->belongsTo(TypeVehicule::class, 'id_type_vehicule');
    }

    public function locationType(): BelongsTo
    {
        return $this->belongsTo(LocationType::class, 'id_type_location');
    }
}
