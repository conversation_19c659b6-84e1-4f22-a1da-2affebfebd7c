<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTransactionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'transaction' => 'required|array',
            'transaction.subscription_id' => 'required|exists:subscriptions,id',
            'transaction.client_id' => 'required|exists:clients,id',
            'transaction.amount' => 'required|numeric|min:0',
            'transaction.id_sale_period' => 'sometimes|nullable|exists:sale_periods,id',
            'transaction.payment_date' => 'required|date',
            'transaction.payment_mode' => 'required|string|in:en_ligne,guichet',
            'transaction.payment_method_id' => 'sometimes|nullable|exists:payment_methods,id',
            'transaction.status' => 'required|string|in:completed,pending,failed,refunded',
            'transaction.transaction_reference' => 'required|string',
            'transaction.online_gateway' => 'nullable|string',
            'transaction.notes' => 'nullable|string',
            'transaction.payment_details' => 'nullable|array',
            'subscription' => 'required|array',
            'subscription.id' => 'required|exists:subscriptions,id',
            'subscription.status' => 'required|string|in:PAYED',
        ];
    }
}