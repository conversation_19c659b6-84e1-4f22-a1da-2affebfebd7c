<?php

namespace App\Repositories;

use App\Models\Establishment;
use Prettus\Repository\Eloquent\BaseRepository;

class EstablishmentRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Establishment::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'cnss_etab' => 'like',
        'abbreviation' => 'like',
        'id_delegation' => '=',
        'id_type_establishment' => '='
    ];
}
