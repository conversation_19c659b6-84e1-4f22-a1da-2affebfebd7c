<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'payment_id' => $this->payment_id,
            'subscription_id' => $this->subscription_id,
            'client_id' => $this->client_id,
            'amount' => $this->amount,
            'payment_date' => $this->payment_date,
            'payment_mode' => $this->payment_mode,
            'payment_method_id' => $this->payment_method_id,
            'status' => $this->status,
            'id_sale_period' => $this->id_sale_period,
            'transaction_reference' => $this->transaction_reference,
            'online_gateway' => $this->online_gateway,
            'sale_point_id' => $this->sale_point_id,
            'employee_id' => $this->employee_id,
            'online_user_agent' => $this->online_user_agent,
            'notes' => $this->notes,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'payment_details' => $this->getRawOriginal('payment_details'),
            
            // Include related resources when they are loaded
            'subscription' => $this->whenLoaded('subscription'),
            'client' => $this->whenLoaded('client'),
            'payment_method' => $this->whenLoaded('paymentMethod'),
            'sale_point' => $this->whenLoaded('salePoint'),
            'employee' => $this->whenLoaded('employee')
        ];
    }
}

