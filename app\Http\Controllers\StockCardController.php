<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateStockCardRequest;
use App\Http\Requests\UpdateStockCardRequest;
use App\Http\Resources\StockCardResource;
use App\Models\StockCard;
use App\Repositories\StockCardRepository;
use App\Services\CardSequenceTracker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class StockCardController extends Controller
{
    public StockCardRepository $repository;
    protected $cardSequenceTracker;

    public function __construct(StockCardRepository $repository, CardSequenceTracker $cardSequenceTracker)
    {
        $this->repository = $repository;
        $this->cardSequenceTracker = $cardSequenceTracker;
        $this->authorizeResource(StockCard::class, 'stock_card');
    }
    public function index(): AnonymousResourceCollection
    {
        return StockCardResource::collection(
            $this->repository
                ->with(['agent', 'cardType'])
                ->latest()
                ->paginate()
        );
    }
    public function all(): AnonymousResourceCollection
    {
        return StockCardResource::collection(
            $this->repository
                ->with(['agent', 'cardType'])
                ->all()
        );
    }
    public function show(StockCard $stockCard): StockCardResource
    {
        return new StockCardResource($stockCard->load(['agent', 'cardType']));
    }
    public function store(CreateStockCardRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $data = [
            'id_card_type' => $validated['id_card_type'],
            'sequence_start' => $validated['sequence_start'],
            'sequence_end' => $validated['sequence_end'],
            'mouvement' => $validated['mouvement'],
        ];

        if ($validated['id_agent'] != null) {
            $data['id_agent'] = $validated['id_agent'];
        }
        $stockCard = $this->repository->create($data);

        if ($validated['mouvement'] === 'ajout') {
            $this->cardSequenceTracker->addToStock(
                $validated['id_card_type'],
                $validated['sequence_start'],
                $validated['sequence_end']
            );
        } elseif ($validated['mouvement'] === 'retour') {
            $this->cardSequenceTracker->returnToStock(
                $validated['id_card_type'],
                $validated['sequence_start'],
                $validated['sequence_end']
            );
        }

        return response()->json([
            'message' => 'Stock card created successfully',
            'data' => new StockCardResource($stockCard->load(['agent', 'cardType']))
        ], Response::HTTP_CREATED);
    }
    public function update(UpdateStockCardRequest $request, StockCard $stockCard): JsonResponse
    {
        $validated = $request->validated();
        $stockCard = $this->repository->update($validated, $stockCard->id);

        return response()->json([
            'message' => 'Stock card updated successfully',
            'data' => new StockCardResource($stockCard->load(['agent', 'cardType']))
        ], Response::HTTP_OK);
    }
    public function destroy(StockCard $stockCard): JsonResponse
    {
        if ($stockCard->mouvement == 'ajout') {
            $exists = $this->repository->findWhere([
                'id_card_type' => $stockCard->id_card_type,
                'mouvement' => 'retour'
            ]);
            if ($exists->isNotEmpty()) {
                return response()->json([
                    'message' => 'Vous ne pouvez pas supprimer cette carte car elle est utilisée dans une retour de carte.',
                ], Response::HTTP_CONFLICT);
            }
        }

        $this->repository->delete($stockCard->id);

        return response()->json([
            'message' => 'Stock card deleted successfully.',
        ], Response::HTTP_OK);
    }
    public function getLatestRecord($id_card_type): JsonResponse
    {
        $stockCard = $this->repository->getLatestRecord($id_card_type);
        if ($stockCard) {
            return response()->json([
                'message' => 'Latest stock card retrieved successfully',
                'data' => new StockCardResource($stockCard)
            ], Response::HTTP_OK);
        }
        return response()->json([
            'message' => 'No stock card found for this card type',
        ], Response::HTTP_NOT_FOUND);
    }
    public function getIntervalleSequence($id_card_type, $debSequence): JsonResponse
    {

        $stockCard = $this->repository->findRecord($id_card_type, $debSequence);
        if ($stockCard) {
            return response()->json([
                'message' => 'Intervalle sequence retrieved successfully',
                'data' => [
                    'sequence_start' => $stockCard->sequence_start,
                    'sequence_end' => $stockCard->sequence_end,
                ]
            ], Response::HTTP_OK);
        }
        return response()->json([
            'message' => 'No stock card found for this card type',
        ], Response::HTTP_NOT_FOUND);
    }
}
