<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDegreeRequest;
use App\Http\Requests\UpdateDegreeRequest;
use App\Http\Resources\DegreeResource;
use App\Models\Degree;
use App\Repositories\DegreeRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DegreeController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(DegreeRepository::class);
      //  $this->authorizeResource(Degree::class, 'degree');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $degrees = $this->repository
                    ->with(['typeEstablishment'])
                    ->latest()
                    ->paginate();
        return DegreeResource::collection($degrees);
    }

    public function all(): AnonymousResourceCollection
    {
        $degrees = $this->repository->with(['typeEstablishment'])->all();
        return DegreeResource::collection($degrees);
    }

    public function store(StoreDegreeRequest $request): JsonResponse
    {
        $degree = $this->repository->create($request->validated());
        $degree->load('typeEstablishment');

        return response()->json([
            'message' => 'Degree created successfully',
            'data' => new DegreeResource($degree)
        ], 201);
    }

    public function show(Degree $degree): DegreeResource
    {
        $degree->load('typeEstablishment');
        return new DegreeResource($degree);
    }

    public function update(UpdateDegreeRequest $request, Degree $degree): JsonResponse
    {
        $degree = $this->repository->update($request->validated(), $degree->id);
        $degree->load('typeEstablishment');

        return response()->json([
            'message' => 'Degree updated successfully',
            'data' => new DegreeResource($degree)
        ]);
    }

    public function destroy(Degree $degree): JsonResponse
    {
        try {
            $this->repository->delete($degree->id);
            return response()->json([
                'message' => 'Degree deleted successfully'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Degree cannot be deleted, it is used in other records'
            ], 422);
        }
    }

    public function getByType($typeId): AnonymousResourceCollection
    {
        $degrees = $this->repository->with(['typeEstablishment'])
            ->findWhere(['id_type_establishment' => $typeId]);
        return DegreeResource::collection($degrees);
    }
}
