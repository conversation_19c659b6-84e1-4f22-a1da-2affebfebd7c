<?php

namespace App\Repositories;

use App\Models\Line;
use Prettus\Repository\Eloquent\BaseRepository;

class LineRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'CODE_LINE' => '=',
        'type_service' => '=',
        'status' => '=',
        'commercial_speed' => '='
    ];

    public function model(): string
    {
        return Line::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
