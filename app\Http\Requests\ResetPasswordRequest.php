<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'exists:subscribers,email'],
            'password' => ['required', 'string', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/'],
            'confirmPassword' => ['required', 'same:password'],
            'verificationCode' => ['required', 'string', 'size:8'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         // Email
    //         'email.required'           => [
    //             'en' => 'The email field is required.',
    //             'fr' => 'Le champ email est obligatoire.',
    //             'ar' => 'حقل البريد الإلكتروني مطلوب.',
    //         ],
    //         'email.email'              => [
    //             'en' => 'The email must be a valid email address.',
    //             'fr' => 'L’email doit être une adresse email valide.',
    //             'ar' => 'يجب أن يكون البريد الإلكتروني عنوانًا صالحًا.',
    //         ],
    //         'email.exists'             => [
    //             'en' => 'The email does not exist.',
    //             'fr' => 'Cet email n’existe pas.',
    //             'ar' => 'البريد الإلكتروني غير موجود.',
    //         ],
    //         // Password
    //         'password.required'        => [
    //             'en' => 'The password field is required.',
    //             'fr' => 'Le champ mot de passe est obligatoire.',
    //             'ar' => 'حقل كلمة المرور مطلوب.',
    //         ],
    //         'password.min'             => [
    //             'en' => 'The password must be at least 8 characters.',
    //             'fr' => 'Le mot de passe doit contenir au moins 8 caractères.',
    //             'ar' => 'يجب ان يحتوي كلمة المرور على 8 حروف.',
    //         ],
    //         'password.regex'           => [
    //             'en' => 'The password must contain at least one lowercase letter, one uppercase letter, one number, and one special character.',
    //             'fr' => 'Le mot de passe doit contenir au moins une lettre minuscule, une lettre majuscule, un chiffre et un caractère spécial.',
    //             'ar' => 'يجب ان يحتوي كلمة المرور على حرف صغير وحرف كبير ورقم وحرف خاص.',
    //         ],
    //         // Confirm Password
    //         'confirmPassword.required' => [
    //             'en' => 'The confirm password field is required.',
    //             'fr' => 'Le champ de confirmation du mot de passe est obligatoire.',
    //             'ar' => 'حقل تاكيد كلمة المرور مطلوب.',
    //         ],
    //         'confirmPassword.same'     => [
    //             'en' => 'The confirm password and password must match.',
    //             'fr' => 'La confirmation du mot de passe et le mot de passe doivent correspondre.',
    //             'ar' => 'يجب ان تتطابق تاكيد كلمة المرور وكلمة المرور.',
    //         ],
    //         // Verification Code
    //         'verificationCode.required' => [
    //             'en' => 'The code field is required.',
    //             'fr' => 'Le champ du code est obligatoire.',
    //             'ar' => 'حقل الرمز مطلوب.',
    //         ],
    //         'verificationCode.size'     => [
    //             'en' => 'The code must be 8 characters.',
    //             'fr' => 'Le code doit contenir 8 caractères.',
    //             'ar' => 'يجب ان يحتوي الرمز على 8 حروف.',
    //         ]
    //     ];
    // }
}
