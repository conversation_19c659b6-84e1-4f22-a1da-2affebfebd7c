<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ConfigResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'key' => $this->key,
            'value' => $this->value,
            'typed_value' => $this->typed_value,
            'type' => $this->type,
            'group' => $this->group,
            'label_fr' => $this->label_fr,
            'label_en' => $this->label_en,
            'label_ar' => $this->label_ar,
            'description_fr' => $this->description_fr,
            'description_en' => $this->description_en,
            'description_ar' => $this->description_ar,
            'is_public' => $this->is_public,
            'is_system' => $this->is_system,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
