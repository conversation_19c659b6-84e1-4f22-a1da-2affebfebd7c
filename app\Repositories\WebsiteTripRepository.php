<?php

namespace App\Repositories;

use App\Models\WebsiteTrip;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class WebsiteTripRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'id_line' => '=',
        'id_station_start' => '=',
        'id_station_end' => '=',
        'status' => '=',
        'number_of_km' => 'between'
    ];

    public function model(): string
    {
        return WebsiteTrip::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
