<?php

namespace App\Repositories;

use App\Models\LocationSeason;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class LocationSeasonRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'start_date' => '>=',
        'end_date' => '<=',
        'status' => '='
    ];

    public function model(): string
    {
        return LocationSeason::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
