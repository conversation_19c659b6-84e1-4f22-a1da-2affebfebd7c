<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Establishment extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'cnss_etab',
        'abbreviation',
        'id_delegation',
        'id_type_establishment',
    ];

    public function delegation(): BelongsTo
    {
        return $this->belongsTo(Delegation::class, 'id_delegation');
    }

    public function typeEstablishment(): BelongsTo
    {
        return $this->belongsTo(TypeEstablishment::class, 'id_type_establishment');
    }
}
