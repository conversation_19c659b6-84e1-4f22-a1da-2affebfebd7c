<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLocationSeasonRequest;
use App\Http\Requests\UpdateLocationSeasonRequest;
use App\Http\Resources\LocationSeasonResource;
use App\Models\LocationSeason;
use App\Repositories\LocationSeasonRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class LocationSeasonController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = resolve(LocationSeasonRepository::class);
        $this->authorizeResource(LocationSeason::class, 'location_season');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $locationSeasons = $this->repository
                            ->latest()
                            ->paginate();
        return LocationSeasonResource::collection($locationSeasons);
    }

    public function all(): AnonymousResourceCollection
    {
        $locationSeasons = $this->repository->all();
        return LocationSeasonResource::collection($locationSeasons);
    }

    public function store(StoreLocationSeasonRequest $request): JsonResponse
    {
        $locationSeason = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Location season created successfully',
            'data' => new LocationSeasonResource($locationSeason)
        ], 201);
    }

    public function show(LocationSeason $locationSeason): LocationSeasonResource
    {
        $locationSeason->load(['typeVehiculeSaisonLocations.typeVehicule']);
        return new LocationSeasonResource($locationSeason);
    }

    public function update(UpdateLocationSeasonRequest $request, LocationSeason $locationSeason): JsonResponse
    {
        $locationSeason = $this->repository->update($request->validated(), $locationSeason->id);

        return response()->json([
            'message' => 'Location season updated successfully',
            'data' => new LocationSeasonResource($locationSeason)
        ]);
    }

    public function destroy(LocationSeason $locationSeason): JsonResponse
    {
        $this->repository->delete($locationSeason->id);
        return response()->json([
            'message' => 'Location season deleted successfully'
        ]);
    }

    public function active(): AnonymousResourceCollection
    {
        $locationSeasons = $this->repository->findWhere(['status' => true]);
        return LocationSeasonResource::collection($locationSeasons);
    }
}
