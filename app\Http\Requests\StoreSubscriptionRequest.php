<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_subs_type' => 'required|exists:subs_types,id',
            'is_stagiaire' => 'required',
            'id_payment_method' => 'nullable|exists:payment_methods,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id|different:id_station_start',
            'id_line' => 'required|exists:lines,id',
            'id_periodicity' => 'nullable|exists:periodicities,id',
            'id_sale_period' => 'nullable|exists:sale_periods,id',
            'is_reversed' => 'required|boolean',
            'photo' => 'nullable',
            'hasVacances' => 'nullable|boolean',
            'rest_days' => 'nullable|array',
            'rest_days.*' => 'integer|min:1|max:7',
            'subs_number' => 'nullable|integer|min:1',
            'status' => 'nullable|in:PAYED,NOTPAYED,CANCELED',
            'id_parent' => 'nullable|exists:subscriptions,id',
            'renewal_date' => 'nullable|date',
            'special_client' => 'nullable|in:CIVIL,SCOLAIRE,UNIVERSITAIRE',
            'stage_date_start' => 'nullable|date',
            'stage_date_end' => 'nullable|date|after_or_equal:stage_date_start',
            'is_printed' => 'nullable|boolean',

            // Client fields
            'firstname' => 'nullable|string|max:255',
            'lastname' => 'nullable|string|max:255',
            'identity_number' => 'required|string',
            'phone' => 'required|numeric',
            'email' => 'nullable|email',
            'address' => 'required|string',
            'dob' => 'nullable|date',
            'id_governorate' => 'required|exists:governorates,id',
            'id_delegation' => 'required|exists:delegations,id',
            'id_establishment' => 'nullable|exists:establishments,id',
            'id_degree' => 'nullable|exists:degrees,id',
        ];
    }
}
