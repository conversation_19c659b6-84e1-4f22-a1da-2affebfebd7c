<?php

namespace App\Repositories;

use App\Models\MotifDuplicate;
use Prettus\Repository\Eloquent\BaseRepository;

class MotifDuplicateRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return MotifDuplicate::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like'
    ];
}

