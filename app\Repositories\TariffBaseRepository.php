<?php

namespace App\Repositories;

use App\Models\TariffBase;
use Prettus\Repository\Eloquent\BaseRepository;

class TariffBaseRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'tariffPerKM' => '=',
        'date' => '=',  
        'id_subs_type' => '=',
        'for_website' => '='
    ];

    public function model(): string
    {
        return TariffBase::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}



