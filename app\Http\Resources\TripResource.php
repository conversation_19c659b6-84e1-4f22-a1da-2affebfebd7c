<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'id_line' => $this->id_line,
            'id_station_start' => $this->id_station_start,
            'id_station_end' => $this->id_station_end,
            'status' => $this->status,
            'inter_station' => $this->inter_station,
            'number_of_km' => $this->number_of_km,
            'line' => new LineResource($this->whenLoaded('line')),
            'station_start' => new StationResource($this->whenLoaded('startStation')),
            'station_end' => new StationResource($this->whenLoaded('endStation')),
            'tariff_options' => TariffOptionResource::collection($this->whenLoaded('tariffOptions')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
