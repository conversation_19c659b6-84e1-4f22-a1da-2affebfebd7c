<?php

namespace App\Services;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VerifService
{
    private $etudiants = [
        ['cin' => '00000001', 'nom' => 'بن أحمد', 'prenom' => 'آمال', 'date_naissance' => '2000-01-01', 'etablissement' => 'جامعة تونس', 'niveau' => 'إجازة', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000002', 'nom' => 'الطرابلسي', 'prenom' => 'سامي', 'date_naissance' => '1999-05-10', 'etablissement' => 'جامعة صفاقس', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'صفاقس', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000003', 'nom' => 'الغربي', 'prenom' => 'نور', 'date_naissance' => '2001-03-12', 'etablissement' => 'جامعة سوسة', 'niveau' => 'إجازة', 'etablissement_delegation' => 'سوسة', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000004', 'nom' => 'منصور', 'prenom' => 'كريم', 'date_naissance' => '1998-07-15', 'etablissement' => 'جامعة المنستير', 'niveau' => 'دكتوراه', 'etablissement_delegation' => 'المنستير', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000005', 'nom' => 'قاسم', 'prenom' => 'سونيا', 'date_naissance' => '2002-11-22', 'etablissement' => 'جامعة قابس', 'niveau' => 'إجازة', 'etablissement_delegation' => 'قابس', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000006', 'nom' => 'ساسي', 'prenom' => 'محمد', 'date_naissance' => '2001-06-17', 'etablissement' => 'جامعة قفصة', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'قفصة', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000007', 'nom' => 'فرسي', 'prenom' => 'إيناس', 'date_naissance' => '2003-09-23', 'etablissement' => 'جامعة القيروان', 'niveau' => 'إجازة', 'etablissement_delegation' => 'القيروان', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000008', 'nom' => 'الهلالي', 'prenom' => 'حسام', 'date_naissance' => '1997-02-04', 'etablissement' => 'جامعة جندوبة', 'niveau' => 'دكتوراه', 'etablissement_delegation' => 'جندوبة', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000009', 'nom' => 'زواري', 'prenom' => 'ليلى', 'date_naissance' => '2000-12-01', 'etablissement' => 'جامعة المنار', 'niveau' => 'إجازة', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000010', 'nom' => 'بوزيد', 'prenom' => 'أنيس', 'date_naissance' => '1998-03-29', 'etablissement' => 'جامعة قرطاج', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'أريانة', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000011', 'nom' => 'دلاعي', 'prenom' => 'أمل', 'date_naissance' => '2001-01-15', 'etablissement' => 'جامعة صفاقس', 'niveau' => 'إجازة', 'etablissement_delegation' => 'صفاقس', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000012', 'nom' => 'شواشي', 'prenom' => 'إيهاب', 'date_naissance' => '1999-12-09', 'etablissement' => 'جامعة تونس', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'تونس', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000013', 'nom' => 'الجبالي', 'prenom' => 'نجلاء', 'date_naissance' => '2000-06-22', 'etablissement' => 'جامعة سوسة', 'niveau' => 'إجازة', 'etablissement_delegation' => 'سوسة', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000014', 'nom' => 'بن عيسى', 'prenom' => 'عماد', 'date_naissance' => '1997-10-10', 'etablissement' => 'جامعة قابس', 'niveau' => 'دكتوراه', 'etablissement_delegation' => 'قابس', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000015', 'nom' => 'كمون', 'prenom' => 'ياسمين', 'date_naissance' => '2002-02-19', 'etablissement' => 'جامعة المنستير', 'niveau' => 'إجازة', 'etablissement_delegation' => 'المنستير', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000016', 'nom' => 'بوستة', 'prenom' => 'زياد', 'date_naissance' => '1998-04-30', 'etablissement' => 'جامعة جندوبة', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'جندوبة', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000017', 'nom' => 'دراجي', 'prenom' => 'رحاب', 'date_naissance' => '2003-05-05', 'etablissement' => 'جامعة تونس', 'niveau' => 'إجازة', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000018', 'nom' => 'مخلوف', 'prenom' => 'آدم', 'date_naissance' => '1999-09-17', 'etablissement' => 'جامعة صفاقس', 'niveau' => 'ماجستير', 'etablissement_delegation' => 'صفاقس', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000019', 'nom' => 'بن فرج', 'prenom' => 'شيراز', 'date_naissance' => '2001-11-01', 'etablissement' => 'جامعة سوسة', 'niveau' => 'إجازة', 'etablissement_delegation' => 'سوسة', 'genre' => 'أنثى', 'annee_scolaire' => '2024-2025'],
        ['cin' => '00000020', 'nom' => 'العكروت', 'prenom' => 'حسين', 'date_naissance' => '1996-08-24', 'etablissement' => 'جامعة قابس', 'niveau' => 'دكتوراه', 'etablissement_delegation' => 'قابس', 'genre' => 'ذكر', 'annee_scolaire' => '2024-2025'],
    ];


    private $eleves = [
        ['identifiant' => '202500000001', 'nom' => 'الشابي', 'prenom' => 'مريم', 'date_naissance' => '2005-01-01', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000002', 'nom' => 'بن عمر', 'prenom' => 'إيهاب', 'date_naissance' => '2005-02-02', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'ذكر', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000003', 'nom' => 'بن سالم', 'prenom' => 'هدى', 'date_naissance' => '2005-03-03', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000004', 'nom' => 'الدريدي', 'prenom' => 'باسل', 'date_naissance' => '2005-04-04', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'ذكر', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000005', 'nom' => 'النقاش', 'prenom' => 'رانية', 'date_naissance' => '2005-05-05', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000006', 'nom' => 'العمري', 'prenom' => 'وسيم', 'date_naissance' => '2005-06-06', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'ذكر', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000007', 'nom' => 'جلاصي', 'prenom' => 'ليلى', 'date_naissance' => '2005-07-07', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000008', 'nom' => 'بن نصر', 'prenom' => 'زياد', 'date_naissance' => '2005-08-08', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000009', 'nom' => 'العروي', 'prenom' => 'هالة', 'date_naissance' => '2005-09-09', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000010', 'nom' => 'سالم', 'prenom' => 'رامي', 'date_naissance' => '2005-10-10', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000011', 'nom' => 'بن زايد', 'prenom' => 'أميرة', 'date_naissance' => '2005-11-11', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000012', 'nom' => 'عبيدي', 'prenom' => 'سيف', 'date_naissance' => '2005-12-12', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000013', 'nom' => 'بن حفص', 'prenom' => 'نجلاء', 'date_naissance' => '2005-01-13', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000014', 'nom' => 'غربي', 'prenom' => 'آدم', 'date_naissance' => '2005-02-14', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000015', 'nom' => 'قروي', 'prenom' => 'إيمان', 'date_naissance' => '2005-03-15', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000016', 'nom' => 'حمدي', 'prenom' => 'أيوب', 'date_naissance' => '2005-04-16', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000017', 'nom' => 'صغير', 'prenom' => 'شيماء', 'date_naissance' => '2005-05-17', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000018', 'nom' => 'بن جمعة', 'prenom' => 'مالك', 'date_naissance' => '2005-06-18', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000019', 'nom' => 'شابي', 'prenom' => 'هدى', 'date_naissance' => '2005-07-19', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
        ['identifiant' => '202500000020', 'nom' => 'عباس', 'prenom' => 'ياسين', 'date_naissance' => '2005-08-20', 'etablissement' => 'المعهد الثانوي النموذجي', 'etablissement_delegation' => 'تونس', 'genre' => 'أنثى', 'niveau' => 'السنة النهائية', 'annee_scolaire' => '2024-2025'],
    ];

    private $civils = [
        ['cin' => '10000000', 'nom' => 'الخميري', 'prenom' => 'فاطمة', 'date_naissance' => '1980-01-01', 'genre' => 'ذكر'],
        ['cin' => '10000001', 'nom' => 'البكوش', 'prenom' => 'سامي', 'date_naissance' => '1981-02-02', 'genre' => 'ذكر'],
        ['cin' => '10000002', 'nom' => 'العربي', 'prenom' => 'ميساء', 'date_naissance' => '1982-03-03', 'genre' => 'ذكر'],
        ['cin' => '10000003', 'nom' => 'بن عمر', 'prenom' => 'كمال', 'date_naissance' => '1983-04-04', 'genre' => 'ذكر'],
        ['cin' => '10000004', 'nom' => 'الجميعي', 'prenom' => 'آمنة', 'date_naissance' => '1984-05-05', 'genre' => 'ذكر'],
        ['cin' => '10000005', 'nom' => 'موسى', 'prenom' => 'بلال', 'date_naissance' => '1985-06-06', 'genre' => 'ذكر'],
        ['cin' => '10000006', 'nom' => 'عطية', 'prenom' => 'رحمة', 'date_naissance' => '1986-07-07', 'genre' => 'ذكر'],
        ['cin' => '10000007', 'nom' => 'بن ساسي', 'prenom' => 'حمزة', 'date_naissance' => '1987-08-08', 'genre' => 'ذكر'],
        ['cin' => '10000008', 'nom' => 'بن سليمان', 'prenom' => 'علياء', 'date_naissance' => '1988-09-09', 'genre' => 'ذكر'],
        ['cin' => '10000009', 'nom' => 'الزغيدي', 'prenom' => 'ياسر', 'date_naissance' => '1989-10-10', 'genre' => 'ذكر'],
        ['cin' => '10000010', 'nom' => 'صميدة', 'prenom' => 'هند', 'date_naissance' => '1980-11-11', 'genre' => 'ذكر'],
        ['cin' => '10000011', 'nom' => 'بن رمضان', 'prenom' => 'شريف', 'date_naissance' => '1981-12-12', 'genre' => 'ذكر'],
        ['cin' => '10000012', 'nom' => 'عبدلي', 'prenom' => 'غادة', 'date_naissance' => '1982-01-13', 'genre' => 'ذكر'],
        ['cin' => '10000013', 'nom' => 'الجويني', 'prenom' => 'مراد', 'date_naissance' => '1983-02-14', 'genre' => 'ذكر'],
        ['cin' => '10000014', 'nom' => 'فرحات', 'prenom' => 'لمياء', 'date_naissance' => '1984-03-15', 'genre' => 'ذكر'],
        ['cin' => '10000015', 'nom' => 'بن علي', 'prenom' => 'نبيل', 'date_naissance' => '1985-04-16', 'genre' => 'ذكر'],
        ['cin' => '10000016', 'nom' => 'عبد المؤمن', 'prenom' => 'سندس', 'date_naissance' => '1986-05-17', 'genre' => 'ذكر'],
        ['cin' => '10000017', 'nom' => 'تليلي', 'prenom' => 'سيف الدين', 'date_naissance' => '1987-06-18', 'genre' => 'ذكر'],
        ['cin' => '10000018', 'nom' => 'كمون', 'prenom' => 'نهى', 'date_naissance' => '1988-07-19', 'genre' => 'ذكر'],
        ['cin' => '10000019', 'nom' => 'بوراوي', 'prenom' => 'حسن', 'date_naissance' => '1989-08-20', 'genre' => 'ذكر'],
    ];

    public function verifEtudiant($id_etud, $dateNaissance)
    {
        $apiPath=env('URL_ENDPOINT_VERIF_ETUDIANT');
        $fileName=env('API_KEY_PATH_VERIF_ETUDIANT');
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'apiKey' => trim(file_get_contents(base_path($fileName))),
        ])->post($apiPath, [
            'id_etud' => $id_etud,
            'date_naissance' => $dateNaissance,
        ]);

        
        if ($response->successful()) {
            $data = $response->json();
            return $data;
        } else {
           return [];
        }
    }
    public function verifCitoyen($cin, $jourNaiss, $moisNaiss, $anneeNaiss)
    {
        $apiPath = env('URL_ENDPOINT_VERIF_CITOYEN', 'https://************/restapi/checkCitizenByCinDnArFr');
        $uxp_client = env('UXP_CLIENT', 'ONI-TN-TEST/GOV/TRANSPORT-SS/MT-SrtNabeul-AbonnementScolaire');
        $uxp_service = env('UXP_SERVICE', 'ONI-TN-TEST/GOV/CNI/CNI-Registre/checkCitizenByCinDn/v1');

        $response = Http::withOptions([
            'verify' => false, // Désactive la vérification SSL comme curl -k
        ])->withHeaders([
            'Content-Type' => 'application/json',
            'uxp-service' => $uxp_service,
            'uxp-client' => $uxp_client,
        ])->post($apiPath, [
            'cin' => $cin,
            'jourNaiss' => $jourNaiss,
            'moisNaiss' => $moisNaiss,
            'anneeNaiss' => $anneeNaiss,
        ]);

        if ($response->successful()) {
            return $response->json();
        }
        return [];
    }
    public function verifCitoyenFamille($idEdu, $jourNaiss, $moisNaiss, $anneeNaiss)
    {
        $apiPath=env('URL_ENDPOINT_VERIF_CITOYEN_FAMILLE');
        $uxp_client=env('UXP_CLIENT_CITOYEN_FAMILLE');
        $uxp_service=env('UXP_SERVICE_CITOYEN_FAMILLE');
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'UXP-SERVICE-KEY' => $uxp_service,
            'UXP-CLIENT-KEY' => $uxp_client,
        ])->post($apiPath, [
            'idEdu' => $idEdu,
            'jourNaiss' => $jourNaiss,
            'moisNaiss' => $moisNaiss,
            'anneeNaiss' => $anneeNaiss,
        ]);
        Log::warning($response);
        if ($response->successful()) {
            $data = $response->json();
            return $data;
        } else {
           return [];
        }
    }

    // public function verifEleve($identifiant, $dateNaissance)
    // {
    //     foreach ($this->eleves as $eleve) {
    //         if ($eleve['identifiant'] === $identifiant && $eleve['date_naissance'] === $dateNaissance) {
    //             return $eleve;
    //         }
    //     }
    //     return false;
    // }

    // public function verifCivile($cin)
    // {
    //     foreach ($this->civils as $civil) {
    //         if ($civil['cin'] === $cin) {
    //             return $civil;
    //         }
    //     }
    //     return false;
    // }
}
