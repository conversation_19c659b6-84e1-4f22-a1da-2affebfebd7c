<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'lastname' => ['required', 'string', 'max:255'],
            'firstname' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'size:8', 'unique:subscribers,phone'],
            'cin' => ['required', 'string', 'size:8', 'unique:subscribers,cin'],
            'address' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:subscribers,email'],
            'password' => ['required', 'string', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/'],
            'confirmPassword' => ['required', 'same:password'],
            'verificationCode' => ['required', 'string', 'size:8'],
        ];
    }

}
