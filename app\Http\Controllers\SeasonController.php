<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSeasonRequest;
use App\Http\Requests\UpdateSeasonRequest;
use App\Http\Resources\SeasonResource;
use App\Models\Season;
use App\Repositories\SeasonRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SeasonController extends Controller
{
    private SeasonRepository $repository;

    public function __construct(SeasonRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Season::class, 'season');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return SeasonResource::collection($this->repository->latest()->paginate($request->input('perPage')));
    }

    public function all(): AnonymousResourceCollection
    {
        return SeasonResource::collection($this->repository->all());
    }

    public function store(StoreSeasonRequest $request): JsonResponse
    {
        $season = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Season created successfully',
            'data' => new SeasonResource($season)
        ], 201);
    }

    public function show(Season $season): SeasonResource
    {
        return new SeasonResource($season);
    }

    public function update(UpdateSeasonRequest $request, Season $season): JsonResponse
    {
        $season = $this->repository->update($request->validated(), $season->id);

        return response()->json([
            'message' => 'Season updated successfully',
            'data' => new SeasonResource($season)
        ]);
    }

    public function destroy(Season $season): JsonResponse
    {
        try {
        $this->repository->delete($season->id);

        return response()->json([
            'message' => 'Season deleted successfully'
        ]);
    } catch (\Throwable $th) {
        return response()->json([
            'message' => 'Season cannot be deleted, it is used in other records'
        ], 422);
    }
    }
}
