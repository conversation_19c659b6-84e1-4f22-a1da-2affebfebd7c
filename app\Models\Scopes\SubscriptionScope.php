<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class SubscriptionScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * This scope filters subscriptions to only show those belonging to the currently
     * authenticated subscriber.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (auth()->check()) {
            $builder->where('id_subscriber', auth()->id());
        }
    }
}
