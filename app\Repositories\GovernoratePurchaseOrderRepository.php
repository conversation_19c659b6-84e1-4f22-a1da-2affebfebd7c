<?php

namespace App\Repositories;

use App\Models\GovernoratePurchaseOrder;
use Prettus\Repository\Eloquent\BaseRepository;

class GovernoratePurchaseOrderRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return GovernoratePurchaseOrder::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'ref' => 'like',
        'initial_amount' => '=',
        'current_amount' => '=',
        'status' => '=',
        'id_governorate' => '='
    ];
}