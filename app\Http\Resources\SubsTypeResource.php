<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubsTypeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'color' => $this->color,
            'is_student' => $this->is_student,
            'hasCIN' => $this->hasCIN,
            'is_impersonal' => $this->is_impersonal,
            'is_conventional' => $this->is_conventional,
            'tariff_options' => TariffOptionResource::collection($this->whenLoaded('tariffOptions')),
            'card_fees' => CardFeeResource::collection($this->whenLoaded('cardFees')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

