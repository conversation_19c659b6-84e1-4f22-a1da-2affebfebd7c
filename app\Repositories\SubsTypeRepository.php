<?php

namespace App\Repositories;

use App\Models\SubsType;
use Prettus\Repository\Eloquent\BaseRepository;

class SubsTypeRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'color' => 'like',
        'is_student' => '=',
        'hasCIN' => '=',
        'is_impersonal' => '=',
        'is_conventional' => '='
    ];

    public function model(): string
    {
        return SubsType::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
