<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSalePeriodRequest;
use App\Http\Requests\UpdateSalePeriodRequest;
use App\Http\Resources\SalePeriodResource;
use App\Models\SalePeriod;
use App\Repositories\SalePeriodRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;

class SalePeriodController extends Controller
{
    private SalePeriodRepository $repository;

    public function __construct(SalePeriodRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SalePeriod::class, 'sale_period');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return SalePeriodResource::collection(
            $this->repository->with(['campaign', 'abnType', 'affectationAgents'])->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return SalePeriodResource::collection(
            $this->repository->with(['campaign', 'affectationAgents'])->all()
        );
    }

    public function store(StoreSalePeriodRequest $request): JsonResponse
    {
        $salePeriod = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Sale period created successfully',
            'data' => new SalePeriodResource($salePeriod->load(['campaign', 'affectationAgents']))
        ], 201);
    }

    public function show(SalePeriod $salePeriod): SalePeriodResource
    {
        return new SalePeriodResource($salePeriod->load(['campaign', 'abnType', 'affectationAgents']));
    }

    public function update(UpdateSalePeriodRequest $request, $id): JsonResponse
    {
        try {
            $salePeriod = $this->repository->find($id);
            if (!$salePeriod) {
                return response()->json([
                    'message' => 'Sale period not found',
                    'error' => 'Resource not found'
                ], Response::HTTP_NOT_FOUND);
            }
            $updated = $this->repository->update($request->validated(), $id);
            return response()->json([
                'message' => 'Sale period updated successfully',
                'data' => new SalePeriodResource($updated->load(['campaign', 'affectationAgents']))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating sale period',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            $salePeriod = $this->repository->find($id);
            if (!$salePeriod) {
                return response()->json([
                    'message' => 'Sale period not found',
                    'error' => 'Resource not found'
                ], Response::HTTP_NOT_FOUND);
            }
            $this->repository->delete($id);
            return response()->json([
                'message' => 'Sale period deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting sale period',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getByCampaign($campaignId): AnonymousResourceCollection
    {
        $salePeriods = $this->repository
            ->with(['campaign', 'affectationAgents'])
            ->findWhere(['id_campaign' => $campaignId]);

        return SalePeriodResource::collection($salePeriods);
    }
}







