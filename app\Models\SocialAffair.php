<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SocialAffair extends Model
{
    protected $fillable = [
        'governorate_id',
        'academic_year_id',
        'delegation',
        'eleve_etudiant',
        'societe',
        'nom_parent',
        'cin_parent',
        'identifier',
        'dob',
        'telephone',
        'nom_complet',
        'niveau_etude',
        'trajet_requise'
    ];

    protected $casts = [
        'dob' => 'date'
    ];

    /**
     * Get the governorate that owns the social affair.
     */
    public function governorate(): BelongsTo
    {
        return $this->belongsTo(Governorate::class, 'governorate_id');
    }

    /**
     * Get the academic year that owns the social affair.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }
}

