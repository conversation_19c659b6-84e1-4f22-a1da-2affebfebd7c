<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GovernoratePurchaseOrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'ref' => $this->ref,
            'initial_amount' => $this->initial_amount,
            'current_amount' => $this->current_amount,
            'status' => $this->status,
            'date' => $this->date->format('Y-m-d'),
            'id_governorate' => $this->id_governorate,
            'governorate' => new GovernorateResource($this->whenLoaded('governorate')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
