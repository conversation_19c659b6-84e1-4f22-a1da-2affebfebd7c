<?php

namespace App\Repositories;

use App\Models\Delegation;
use Prettus\Repository\Eloquent\BaseRepository;

class DelegationRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Delegation::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'id_governorate' => '='
    ];
}

