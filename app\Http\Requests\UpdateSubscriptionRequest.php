<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubscriptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_subs_type' => 'sometimes|exists:subs_types,id',
            'id_client' => 'sometimes|exists:clients,id',
            'id_payment_method' => 'sometimes|exists:payment_methods,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'id_station_start' => 'nullable|exists:stations,id',
            'id_station_end' => 'nullable|exists:stations,id',
            'id_line' => 'nullable|exists:lines,id',
            'id_periodicity' => 'nullable|exists:periodicities,id',
            'is_reversed' => 'boolean',
            'id_sale_period' => 'nullable|exists:sale_periods,id',
            'photo' => 'nullable',
            'social_affair' => 'boolean',
            'hasVacances' => 'nullable|boolean',
            'rest_days' => 'nullable',
            'rest_days.*' => 'integer|min:1|max:7',
            'subs_number' => 'nullable|integer|min:1',
            'status' => 'nullable|in:PAYED,NOTPAYED,CANCELED',
            'id_parent' => 'nullable|exists:subscriptions,id',
            'renewal_date' => 'nullable|date',

             // Client fields
            'firstname' => 'nullable|string|max:255',
            'lastname' => 'nullable|string|max:255',
            'identity_number' => 'nullable|string',
            'phone' => 'nullable|numeric',
            'email' => 'nullable|email',
            'address' => 'nullable|string',
            'dob' => 'nullable|date',
            'id_governorate' => 'nullable|exists:governorates,id',
            'id_delegation' => 'nullable|exists:delegations,id',
            'id_establishment' => 'nullable|exists:establishments,id',
            'id_degree' => 'nullable|exists:degrees,id',
        ];
    }
}
