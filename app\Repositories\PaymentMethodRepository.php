<?php

namespace App\Repositories;

use App\Models\PaymentMethod;
use Prettus\Repository\Eloquent\BaseRepository;

class PaymentMethodRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'status' => '='
    ];

    public function model(): string
    {
        return PaymentMethod::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}