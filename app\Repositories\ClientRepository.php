<?php

namespace App\Repositories;

use App\Models\Client;
use Prettus\Repository\Eloquent\BaseRepository;

class ClientRepository extends BaseRepository
{
    protected $cacheKey = '';

    public function model(): string
    {
        return Client::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }

    protected $fieldSearchable = [
        'lastname' => 'like',
        'firstname' => 'like',
        'society_name' => 'like',
        'legal_representative' => 'like',
        'phone' => '=',
        'identity_number' => '=',
        'email' => 'like',
        'id_client_type' => '=',
        'id_delegation' => '=',
        'id_governorate' => '=',
        'id_establishment' => '=',
        'id_degree' => '=',
        'is_moral' => '=',
        'is_withTVA' => '='
    ];
}
