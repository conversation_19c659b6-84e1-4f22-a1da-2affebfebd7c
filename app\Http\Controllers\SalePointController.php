<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSalePointRequest;
use App\Http\Requests\UpdateSalePointRequest;
use App\Http\Resources\SalePointResource;
use App\Models\SalePoint;
use App\Models\Agency;
use App\Repositories\SalePointRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SalePointController extends Controller
{
    private SalePointRepository $repository;

    public function __construct(SalePointRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(SalePoint::class, 'sale_point');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return SalePointResource::collection(
            $this->repository->with(['delegation', 'governorate', 'agency'])
                ->withCount(['subsCards', 'affectationAgents'])
                ->latest()
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return SalePointResource::collection(
            $this->repository->all()
        );
    }

    public function store(StoreSalePointRequest $request): JsonResponse
    {
        $salePoint = $this->repository->create($request->validated());

        return response()->json([
            'message' => 'Sale point created successfully',
            'data' => new SalePointResource(
                $salePoint->load(['delegation', 'governorate', 'agency'])
            )
        ], 201);
    }

    public function show(SalePoint $salePoint): SalePointResource
    {
        return new SalePointResource(
            $salePoint->load(['delegation', 'governorate', 'agency'])
                ->loadCount(['subsCards', 'affectationAgents'])
        );
    }

    public function update(UpdateSalePointRequest $request, SalePoint $salePoint): JsonResponse
    {
        $salePoint = $this->repository->update($request->validated(), $salePoint->id);

        return response()->json([
            'message' => 'Sale point updated successfully',
            'data' => new SalePointResource(
                $salePoint->load(['delegation', 'governorate', 'agency'])
            )
        ]);
    }

    public function destroy(SalePoint $salePoint): JsonResponse
    {
        $this->repository->delete($salePoint->id);

        return response()->json([
            'message' => 'Sale point deleted successfully'
        ]);
    }

    public function getByAgency(Agency $agency): AnonymousResourceCollection
    {
        $salePoints = $this->repository->with(['delegation', 'governorate', 'agency'])
            ->withCount(['subsCards', 'affectationAgents'])
            ->where('id_agency', $agency->id)
            ->get();

        return SalePointResource::collection($salePoints);
    }
}
