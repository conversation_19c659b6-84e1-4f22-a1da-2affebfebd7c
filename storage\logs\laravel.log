[2025-06-30 13:23:54] local.ERROR: Command "miorate" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"miorate\" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status at C:\\laragon\\www\\srtgn-backend-public\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 C:\\laragon\\www\\srtgn-backend-public\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('miorate')
#1 C:\\laragon\\www\\srtgn-backend-public\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\laragon\\www\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\laragon\\www\\srtgn-backend-public\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
