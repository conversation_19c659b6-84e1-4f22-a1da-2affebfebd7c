<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Campaign extends Model
{
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    public function salePeriods(): HasMany
    {
        return $this->hasMany(SalePeriod::class, 'id_campaign');
    }
}
