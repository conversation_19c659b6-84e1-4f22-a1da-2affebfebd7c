<?php

namespace App\Http\Controllers;

use App\Models\Discount;
use App\Http\Resources\DiscountResource;
use App\Http\Requests\StoreDiscountRequest;
use App\Http\Requests\UpdateDiscountRequest;
use App\Repositories\DiscountRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class DiscountController extends Controller
{
    private DiscountRepository $repository;

    public function __construct(DiscountRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Discount::class, 'discount');
    }

    public function index(): AnonymousResourceCollection
    {
        return DiscountResource::collection(
            $this->repository
                        ->with(['periodicities', 'subsType'])
                        ->latest()
                        ->paginate()
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return DiscountResource::collection(
            $this->repository->with(['periodicities', 'subsType'])->all()
        );
    }

    public function store(StoreDiscountRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $discount = $this->repository->create([
                'nom_fr' => $request->nom_fr,
                'nom_en' => $request->nom_en,
                'nom_ar' => $request->nom_ar,
                'is_stagiaire' => $request->is_stagiaire,
                'is_affaire_sociale' => $request->is_affaire_sociale,
                'special_client' => $request->special_client,
                'date_start' => $request->date_start,
                'date_end' => $request->date_end,
                'percentage' => $request->percentage,
                'id_subs_type' => $request->id_subs_type,
            ]);

            // Attach periodicities if provided
            if ($request->has('id_periodicities') && is_array($request->id_periodicities)) {
                $discount->periodicities()->attach($request->id_periodicities);
            }

            DB::commit();

            return response()->json([
                'message' => 'Discount created successfully',
                'data' => new DiscountResource(
                    $discount->load(['periodicities', 'subsType'])
                )
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating discount',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Discount $discount): DiscountResource
    {
        return new DiscountResource(
            $discount->load(['periodicities', 'subsType'])
        );
    }

    public function update(UpdateDiscountRequest $request, Discount $discount): JsonResponse
    {
        try {
            DB::beginTransaction();

            $discount = $this->repository->update($request->validated(), $discount->id);

            // Update periodicities if provided
            if ($request->has('id_periodicities')) {
                $discount->periodicities()->sync($request->id_periodicities);
            }

            DB::commit();

            return response()->json([
                'message' => 'Discount updated successfully',
                'data' => new DiscountResource(
                    $discount->load(['periodicities', 'subsType'])
                )
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating discount',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Discount $discount): JsonResponse
    {
        $this->repository->delete($discount->id);

        return response()->json([
            'message' => 'Discount deleted successfully'
        ]);
    }
}



