<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordStepOneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'exists:subscribers,email'],
        ];
    }

    // public function messages(): array
    // {
    //     return [
    //         // Email
    //         'email.required'           => [
    //             'en' => 'The email field is required.',
    //             'fr' => 'Le champ email est obligatoire.',
    //             'ar' => 'حقل البريد الإلكتروني مطلوب.',
    //         ],
    //         'email.email'              => [
    //             'en' => 'The email must be a valid email address.',
    //             'fr' => 'L’email doit être une adresse email valide.',
    //             'ar' => 'يجب أن يكون البريد الإلكتروني عنوانًا صالحًا.',
    //         ],
    //         'email.exists'             => [
    //             'en' => 'The email does not exist.',
    //             'fr' => 'Cet email n’existe pas.',
    //             'ar' => 'البريد الإلكتروني غير موجود.',
    //         ],
    //     ];
    // }
}
