<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TariffBaseResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'tariffPerKM' => $this->tariffPerKM,
            'date' => $this->date,
            'id_subs_type' => $this->id_subs_type,
            'for_website' => $this->for_website,
            'subs_type' => new SubsTypeResource($this->whenLoaded('subsType')),
            'tariff_options' => TariffOptionResource::collection($this->whenLoaded('tariffOptions')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}


