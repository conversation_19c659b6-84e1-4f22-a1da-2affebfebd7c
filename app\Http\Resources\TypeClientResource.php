<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TypeClientResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'is_student' => $this->is_student,
            'color' => $this->color,
            'hasCIN' => $this->hasCIN,
            'is_impersonal' => $this->is_impersonal,
            'is_conventional' => $this->is_conventional,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'clients_count' => $this->when($this->clients_count !== null, $this->clients_count),
            'clients' => ClientResource::collection($this->whenLoaded('clients'))
        ];
    }
}
