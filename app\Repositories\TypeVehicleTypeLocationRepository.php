<?php

namespace App\Repositories;

use App\Models\TypeVehicleTypeLocation;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class TypeVehicleTypeLocationRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_type_vehicule' => '=',
        'id_type_location' => '=',
        'km_min' => '=',
        'status' => '='
    ];

    public function model(): string
    {
        return TypeVehicleTypeLocation::class;
    }

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
