<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TypeVehiculeSaisonLocationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_type_vehicule' => $this->id_type_vehicule,
            'id_saison_location' => $this->id_saison_location,
            'prix_km' => $this->prix_km,
            'status' => $this->status,
            'type_vehicule' => new TypeVehiculeResource($this->whenLoaded('typeVehicule')),
            'season' => new SeasonResource($this->whenLoaded('season')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
