<?php

namespace App\Repositories;

use App\Models\Agency;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;

class AgencyRepository extends BaseRepository
{
    protected $cacheKey = '';
    
    public function model(): string
    {
        return Agency::class;
    }
    
    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
    
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'code' => 'like',
        'contact' => 'like',
        'address' => 'like',
        'id_delegation' => '=',
        'id_governorate' => '='
    ];
}

