<?php

namespace App\Repositories;

use App\Models\CardType;
use Prettus\Repository\Eloquent\BaseRepository;

class CardTypeRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'code' => 'like'
    ];

    public function model(): string
    {
        return CardType::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}